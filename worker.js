// Cloudflare Worker 代理脚本
export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    
    // 只处理 /api/ 路径
    if (!url.pathname.startsWith('/api/')) {
      return new Response('Not Found', { status: 404 });
    }
    
    // 构建目标URL
    const targetUrl = `https://nodeapi.knockdream.com${url.pathname}${url.search}`;
    
    // 转发请求
    const response = await fetch(targetUrl, {
      method: request.method,
      headers: request.headers,
      body: request.body,
    });
    
    // 创建新的响应并添加CORS头
    const newResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    });
    
    // 添加CORS头
    newResponse.headers.set('Access-Control-Allow-Origin', '*');
    newResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    newResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type');
    
    return newResponse;
  },
}; 