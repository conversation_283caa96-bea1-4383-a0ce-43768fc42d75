<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aurora Data Test</title>
</head>
<body>
    <h1>Aurora Data Processing Test</h1>
    <div id="results"></div>
    
    <script>
        // Test data processing functions
        function getCords180(long360, lat360) {
            const lon180 = long360 > 180 ? long360 - 360 : long360;
            const lat180 = lat360 > 180 ? lat360 - 360 : lat360;
            return [lon180, lat180];
        }
        
        function processAuroraData(data) {
            if (!data || !data.coordinates) return { northern: [], southern: [] };
            
            const northernData = [];
            const southernData = [];
            const coordinates = data.coordinates;
            
            for (const coord of coordinates) {
                const [longitude360, latitude360, intensity] = coord;
                const [longitude, latitude] = getCords180(longitude360, latitude360);
                
                if (intensity > 0) {
                    const point = { longitude, latitude, intensity };
                    
                    if (latitude >= 0) {
                        northernData.push(point);
                    } else {
                        southernData.push(point);
                    }
                }
            }
            
            return { northern: northernData, southern: southernData };
        }
        
        // Test with sample data
        const testData = {
            coordinates: [
                [10, 70, 5],    // Northern hemisphere
                [350, 290, 8],  // Southern hemisphere (converted from 360 system)
                [180, 65, 12],  // Northern hemisphere
                [200, 320, 3],  // Southern hemisphere
                [0, 0, 0]       // No activity - should be filtered out
            ]
        };
        
        const result = processAuroraData(testData);
        
        document.getElementById('results').innerHTML = `
            <h2>Test Results:</h2>
            <h3>Northern Hemisphere (${result.northern.length} points):</h3>
            <pre>${JSON.stringify(result.northern, null, 2)}</pre>
            
            <h3>Southern Hemisphere (${result.southern.length} points):</h3>
            <pre>${JSON.stringify(result.southern, null, 2)}</pre>
        `;
        
        console.log('Aurora data processing test completed:', result);
    </script>
</body>
</html>
