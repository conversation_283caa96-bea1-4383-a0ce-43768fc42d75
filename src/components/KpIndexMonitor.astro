---
---
<section id="kp-index" class="py-12 scroll-mt-20">
  <div class="container-custom">
    <div class="text-center mb-10">
      <h2 class="text-3xl font-bold mb-4">KP Index Monitor</h2>
      <p class="text-gray-400 max-w-2xl mx-auto">
        Track the geomagnetic KP index in real-time. Higher values indicate stronger geomagnetic activity and better chances to see aurora.
      </p>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Current KP Index Card -->
      <div class="card p-6">
        <h3 class="text-xl font-semibold mb-4">Current KP Index</h3>
        <div class="flex items-center justify-center h-40">
          <div id="current-kp-large" class="text-[80px] font-bold text-primary-300">-</div>
        </div>
        <div class="mt-4 text-center">
          <div id="kp-status" class="text-lg font-medium">Waiting for data...</div>
          <div id="noaa-scale" class="text-sm text-gray-400 mt-1">-</div>
          <p class="text-sm text-gray-400 mt-2">Updated <span id="kp-updated-time">-</span></p>
        </div>
      </div>
      
      <!-- KP Index Chart -->
      <div class="card p-6 lg:col-span-2">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold">KP Index Forecast</h3>
          <div class="flex space-x-2">
            <button id="btn-24h" class="tab active">24 Hours</button>
            <button id="btn-3d" class="tab">3 Days</button>
            <button id="btn-7d" class="tab">7 Days</button>
          </div>
        </div>
        
        <div class="h-64 relative">
          <div id="kp-chart-loader" class="absolute inset-0 flex items-center justify-center bg-dark-800/80 backdrop-blur-sm">
            <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-400"></div>
          </div>
          <canvas id="kp-chart"></canvas>
        </div>
        
        <!-- NOAA Scale Legend -->
        <div class="grid grid-cols-5 gap-2 mt-6">
          <div class="text-center">
            <div class="w-full h-3 bg-green-500 rounded"></div>
            <span class="text-xs text-gray-400 mt-1">KP 0-4 (Quiet)</span>
          </div>
          <div class="text-center">
            <div class="w-full h-3 bg-yellow-500 rounded"></div>
            <span class="text-xs text-gray-400 mt-1">KP 5 (G1)</span>
          </div>
          <div class="text-center">
            <div class="w-full h-3 bg-orange-500 rounded"></div>
            <span class="text-xs text-gray-400 mt-1">KP 6 (G2)</span>
          </div>
          <div class="text-center">
            <div class="w-full h-3 bg-red-500 rounded"></div>
            <span class="text-xs text-gray-400 mt-1">KP 7 (G3)</span>
          </div>
          <div class="text-center">
            <div class="w-full h-3 bg-purple-500 rounded"></div>
            <span class="text-xs text-gray-400 mt-1">KP 8-9 (G4-G5)</span>
          </div>
        </div>
      </div>
      
      <!-- KP Index Information -->
      <div class="card p-6 lg:col-span-3">
        <h3 class="text-xl font-semibold mb-4">Understanding the KP Index & NOAA Scales</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h4 class="font-medium mb-2">What is the KP Index?</h4>
            <p class="text-sm text-gray-300">
              The KP index is a global geomagnetic storm index that measures the disturbance of Earth's magnetic field caused by solar wind. It ranges from 0 (very little activity) to 9 (extreme geomagnetic storm).
            </p>
          </div>
          <div>
            <h4 class="font-medium mb-2">NOAA Geomagnetic Scales</h4>
            <ul class="text-sm text-gray-300 space-y-1">
              <li><span class="text-yellow-400">G1 (KP 5):</span> Minor storm - Aurora visible in northern regions</li>
              <li><span class="text-orange-400">G2 (KP 6):</span> Moderate storm - Aurora extends to mid-latitudes</li>
              <li><span class="text-red-400">G3 (KP 7):</span> Strong storm - Aurora visible in lower latitudes</li>
              <li><span class="text-purple-400">G4-G5 (KP 8-9):</span> Severe to extreme storms</li>
            </ul>
          </div>
          <div>
            <h4 class="font-medium mb-2">Aurora Viewing Tips</h4>
            <ul class="text-sm text-gray-300 space-y-1">
              <li>Check KP index forecasts regularly</li>
              <li>Find dark locations away from city lights</li>
              <li>Best viewing hours are typically 10 PM - 2 AM local time</li>
              <li>Allow 20-30 minutes for your eyes to adjust to darkness</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  // Import Chart.js with necessary adapters
  import Chart from 'chart.js/auto';
  import 'chartjs-adapter-date-fns';
  import { format, parseISO } from 'date-fns';
  
  // Import timezone utilities
  import { formatTime, addTimezoneChangeListener } from '../utils/timezone.js';
  
  // Get KP color based on value and NOAA scale
  function getKPColor(kp: number, noaaScale: string | null = null): string {
    if (noaaScale) {
      switch (noaaScale) {
        case 'G1': return '#FACC15'; // Yellow
        case 'G2': return '#F97316'; // Orange  
        case 'G3': return '#EF4444'; // Red
        case 'G4': return '#A855F7'; // Purple
        case 'G5': return '#7C3AED'; // Deep Purple
        default: return '#22C55E'; // Green for no scale
      }
    }
    
    // Fallback color mapping based on KP value
    if (kp < 5) return '#22C55E';      // Green
    if (kp === 5) return '#FACC15';    // Yellow (G1)
    if (kp < 7) return '#F97316';      // Orange (G2)
    if (kp < 8) return '#EF4444';      // Red (G3)
    return '#A855F7';                  // Purple (G4+)
  }
  
  // Get KP status text based on NOAA scale
  function getKpStatusText(kp: number, noaaScale: string | null = null): string {
    if (noaaScale) {
      const scaleNames: Record<string, string> = {
        'G1': 'Minor Storm',
        'G2': 'Moderate Storm', 
        'G3': 'Strong Storm',
        'G4': 'Severe Storm',
        'G5': 'Extreme Storm'
      };
      return scaleNames[noaaScale] || 'Quiet';
    }
    
    if (kp < 4) return 'Quiet';
    if (kp < 5) return 'Active'; 
    if (kp === 5) return 'Minor Storm';
    if (kp < 8) return 'Major Storm';
    return 'Severe Storm';
  }
  
  // Import API functions
  import { fetchKpIndex } from '../utils/api.js';
  
  // Initialize KP chart
  let kpChart: any = null;
  let currentView: string = '24h'; // Track current active view
  
  // Update current KP display
  function updateCurrentKp(data: any[]) {
    if (data && data.length > 0) {
      const latestData = data[data.length - 1];
      const kpValue = latestData.kp;
      const noaaScale = latestData.noaa_scale;
      const timestamp = new Date(latestData.time_tag);
      
      // Update large KP display
      const currentKpElement = document.getElementById('current-kp-large');
      if (currentKpElement) {
        currentKpElement.textContent = kpValue;
        currentKpElement.style.color = getKPColor(kpValue, noaaScale);
      }
      
      // Update status
      const kpStatusElement = document.getElementById('kp-status');
      if (kpStatusElement) {
        kpStatusElement.textContent = getKpStatusText(kpValue, noaaScale);
        kpStatusElement.style.color = getKPColor(kpValue, noaaScale);
      }
      
      // Update NOAA scale
      const noaaScaleElement = document.getElementById('noaa-scale');
      if (noaaScaleElement) {
        noaaScaleElement.textContent = noaaScale ? `NOAA Scale: ${noaaScale}` : 'No geomagnetic storm';
      }
      
      // Update timestamp
      const timeElement = document.getElementById('kp-updated-time');
      if (timeElement) {
        timeElement.textContent = formatTime(timestamp);
      }
    }
  }
  
  // Create time-based KP chart
  function createTimeBasedKpChart(data: any[], timeUnit: string = 'hour') {
    const ctx = document.getElementById('kp-chart') as HTMLCanvasElement;
    if (!ctx) return;
    
    if (kpChart) {
      kpChart.destroy();
    }
    
    // Process data for time-based display with timezone
    const processedData = data.map(item => ({
      x: formatTime(new Date(item.time_tag)),
      y: item.kp,
      noaaScale: item.noaa_scale
    }));
    
    kpChart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: processedData.map(item => item.x),
        datasets: [{
          label: 'KP Index',
          data: processedData.map(item => item.y),
          backgroundColor: processedData.map(item => getKPColor(item.y, item.noaaScale)),
          borderColor: processedData.map(item => getKPColor(item.y, item.noaaScale)),
          borderWidth: 1,
          borderRadius: 4,
          maxBarThickness: 25
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          intersect: false,
          mode: 'index'
        },
        scales: {
          x: {
            ticks: {
              color: 'rgba(255, 255, 255, 0.7)',
              maxRotation: 45,
              minRotation: 0,
              maxTicksLimit: 12
            },
            grid: {
              display: false
            }
          },
          y: {
            beginAtZero: true,
            max: 9,
            ticks: {
              stepSize: 1,
              color: 'rgba(255, 255, 255, 0.7)'
            },
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            title: {
              display: true,
              text: 'KP Index',
              color: 'rgba(255, 255, 255, 0.7)'
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              title: function(tooltipItems: any[]) {
                const dataIndex = tooltipItems[0].dataIndex;
                const originalData = data[dataIndex];
                if (originalData) {
                  return formatTime(new Date(originalData.time_tag));
                }
                return tooltipItems[0].label;
              },
              label: function(context: any) {
                const dataIndex = context.dataIndex;
                const originalData = data[dataIndex];
                if (originalData) {
                  const kpValue = originalData.kp;
                  const noaaScale = originalData.noaa_scale;
                  const status = getKpStatusText(kpValue, noaaScale);
                  
                  let label = `KP: ${kpValue} - ${status}`;
                  if (noaaScale) {
                    label += ` (${noaaScale})`;
                  }
                  return label;
                }
                return `KP: ${context.raw}`;
              }
            },
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: 'white',
            bodyColor: 'white',
            borderColor: 'rgba(255, 255, 255, 0.2)',
            borderWidth: 1
          }
        }
      }
    });
  }
  
  // Show 24-hour KP data
  async function show24HourKpData() {
    currentView = '24h';
    const loader = document.getElementById('kp-chart-loader');
    if (loader) loader.classList.remove('hidden');
    
    try {
      const data = await fetchKpIndex();
      if (data && data.length > 0) {
        updateCurrentKp(data);
        
        // Filter to recent data (last 24 hours)
        const now = new Date();
        const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        
                  const recentData = data.filter(item => {
            const itemDate = new Date(item.time_tag);
            return itemDate >= yesterday;
          }).sort((a, b) => new Date(a.time_tag).getTime() - new Date(b.time_tag).getTime());
        
        createTimeBasedKpChart(recentData, 'hour');
      }
    } catch (error) {
      console.error('Error fetching 24h KP data:', error);
    } finally {
      if (loader) loader.classList.add('hidden');
    }
  }
  
  // Show 3-day KP data
  async function show3DayKpData() {
    currentView = '3d';
    const loader = document.getElementById('kp-chart-loader');
    if (loader) loader.classList.remove('hidden');
    
    try {
      const data = await fetchKpIndex();
      if (data && data.length > 0) {
        // Filter to last 3 days of data
        const now = new Date();
        const threeDaysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);
        
                  const recentData = data.filter(item => {
            const itemDate = new Date(item.time_tag);
            return itemDate >= threeDaysAgo;
          }).sort((a, b) => new Date(a.time_tag).getTime() - new Date(b.time_tag).getTime());
        
        createTimeBasedKpChart(recentData, 'hour');
      }
    } catch (error) {
      console.error('Error fetching 3-day KP data:', error);
    } finally {
      if (loader) loader.classList.add('hidden');
    }
  }
  
  // Show 7-day KP data
  async function show7DayKpData() {
    currentView = '7d';
    const loader = document.getElementById('kp-chart-loader');
    if (loader) loader.classList.remove('hidden');
    
    try {
      const data = await fetchKpIndex();
      if (data && data.length > 0) {
        // Filter to last 7 days of data
        const now = new Date();
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        
                  const recentData = data.filter(item => {
            const itemDate = new Date(item.time_tag);
            return itemDate >= sevenDaysAgo;
          }).sort((a, b) => new Date(a.time_tag).getTime() - new Date(b.time_tag).getTime());
        
        createTimeBasedKpChart(recentData, 'day');
      }
    } catch (error) {
      console.error('Error fetching 7-day KP data:', error);
    } finally {
      if (loader) loader.classList.add('hidden');
    }
  }
  
  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    // Default to 24-hour view
    show24HourKpData();
    
    // Function to refresh current view
    function refreshCurrentView() {
      switch (currentView) {
        case '24h':
          show24HourKpData();
          break;
        case '3d':
          show3DayKpData();
          break;
        case '7d':
          show7DayKpData();
          break;
        default:
          show24HourKpData();
      }
    }
    
    // Listen for timezone changes and refresh current view
    addTimezoneChangeListener(() => {
      refreshCurrentView();
    });
    
    // Set up tab buttons
    const btn24h = document.getElementById('btn-24h');
    const btn3d = document.getElementById('btn-3d');
    const btn7d = document.getElementById('btn-7d');
    
    if (btn24h) {
      btn24h.addEventListener('click', (e) => {
        document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
        (e.target as HTMLElement).classList.add('active');
        show24HourKpData();
      });
    }
    
    if (btn3d) {
      btn3d.addEventListener('click', (e) => {
        document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
        const target = e.target as HTMLElement;
        if (target) target.classList.add('active');
        show3DayKpData();
      });
    }
    
    if (btn7d) {
      btn7d.addEventListener('click', (e) => {
        document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
        const target = e.target as HTMLElement;
        if (target) target.classList.add('active');
        show7DayKpData();
      });
    }
    
    // Refresh data every 15 minutes
    setInterval(show24HourKpData, 15 * 60 * 1000);
  });
</script>

<style>
  /* Tab styles */
  .tab {
    @apply px-4 py-2 rounded-md text-sm font-medium transition-all duration-200;
    @apply bg-dark-700 text-gray-300 hover:bg-dark-600 hover:text-white;
  }
  
  .tab.active {
    @apply bg-primary-600 text-white;
  }
</style>