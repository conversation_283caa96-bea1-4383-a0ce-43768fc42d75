---
---
<section id="aurora-power" class="py-12 scroll-mt-20">
  <div class="container-custom">
    <div class="text-center mb-10">
      <h2 class="text-3xl font-bold mb-4">Aurora Power Index</h2>
      <p class="text-gray-400 max-w-2xl mx-auto">
        Monitor the power and intensity of aurora activity in real-time with our HPI (Hemispheric Power Index) chart.
      </p>
    </div>
    
    <div class="card p-6">
      <div class="h-80 relative">
        <div id="power-chart-loader" class="absolute inset-0 flex items-center justify-center bg-dark-800/80 backdrop-blur-sm z-10">
          <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-400"></div>
        </div>
        <canvas id="aurora-power-chart"></canvas>
      </div>
      
      <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="p-4 bg-dark-700/50 rounded-lg">
          <h3 class="font-semibold mb-2">Understanding Aurora Power</h3>
          <p class="text-sm text-gray-300">
            The Hemispheric Power Index (HPI) measures the total energy input into the Earth's atmosphere in gigawatts (GW). 
            Higher values indicate stronger aurora activity and better visibility. Typically, values above 40 GW can produce 
            visible aurora in high latitude regions.
          </p>
        </div>
        
        <div class="p-4 bg-dark-700/50 rounded-lg">
          <h3 class="font-semibold mb-2">Power Thresholds</h3>
          <div class="grid grid-cols-2 gap-2">
            <div>
              <div class="flex items-center">
                <div class="w-3 h-3 rounded-full bg-green-400 mr-2"></div>
                <span class="text-xs text-gray-300">10-20 GW: Weak</span>
              </div>
              <div class="flex items-center mt-1">
                <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                <span class="text-xs text-gray-300">20-40 GW: Moderate</span>
              </div>
            </div>
            <div>
              <div class="flex items-center">
                <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                <span class="text-xs text-gray-300">40-80 GW: Strong</span>
              </div>
              <div class="flex items-center mt-1">
                <div class="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                <span class="text-xs text-gray-300">80+ GW: Very Strong</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  import 'chart.js/auto';
  import 'chartjs-adapter-date-fns';
  import annotationPlugin from 'chartjs-plugin-annotation';
  import { format, parseISO } from 'date-fns';
  import { Chart } from 'chart.js';
  
  // Import timezone utilities
  import { formatTime, addTimezoneChangeListener } from '../utils/timezone.js';
  
  // Register annotation plugin
  Chart.register(annotationPlugin);
  
  // Aurora power chart instance
  let powerChart = null;
  
  // Fetch aurora power data
  async function fetchAuroraPowerData() {
    try {
      const loader = document.getElementById('power-chart-loader');
      loader.classList.remove('hidden');
      
      const response = await fetch('/api/aurora-power');
      if (!response.ok) throw new Error('Failed to fetch aurora power data');
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching aurora power data:', error);
      return [];
    } finally {
      const loader = document.getElementById('power-chart-loader');
      loader.classList.add('hidden');
    }
  }
  
  // Get color based on power value
  function getPowerColor(power) {
    if (power < 20) return 'rgba(76, 175, 80, 0.2)';  // Green, low
    if (power < 40) return 'rgba(76, 175, 80, 0.5)';  // Green, medium
    if (power < 80) return 'rgba(255, 235, 59, 0.5)'; // Yellow
    return 'rgba(244, 67, 54, 0.5)';                 // Red, high
  }
  
  // Create aurora power chart
  function createAuroraPowerChart(timestamps, powerValues) {
    const ctx = document.getElementById('aurora-power-chart');
    
    if (powerChart) {
      powerChart.destroy();
    }
    
    // Find the current time position
    const now = new Date();
    const currentTimeIndex = timestamps.findIndex(time => new Date(time) > now);
    
    // Generate gradient color
    const gradient = ctx.getContext('2d').createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, 'rgba(76, 175, 80, 0.5)');   // Green at low values
    gradient.addColorStop(0.5, 'rgba(255, 235, 59, 0.5)'); // Yellow in the middle
    gradient.addColorStop(1, 'rgba(244, 67, 54, 0.5)');   // Red at high values
    
    powerChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: timestamps,
        datasets: [{
          label: 'Northern Hemisphere Power (GW)',
          data: powerValues,
          borderColor: '#9C27B0',
          backgroundColor: gradient,
          borderWidth: 2,
          pointRadius: 3,
          pointBackgroundColor: '#9C27B0',
          tension: 0.4,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              color: 'rgba(255, 255, 255, 0.7)'
            },
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            title: {
              display: true,
              text: 'Power (GW)',
              color: 'rgba(255, 255, 255, 0.7)'
            }
          },
          x: {
            ticks: {
              color: 'rgba(255, 255, 255, 0.7)',
              maxRotation: 45,
              minRotation: 45
            },
            grid: {
              display: false
            }
          }
        },
        plugins: {
          annotation: {
            annotations: {
              nowLine: {
                type: 'line',
                scaleID: 'x',
                value: new Date(),
                borderColor: 'rgba(255, 255, 255, 0.7)',
                borderWidth: 2,
                borderDash: [5, 5],
                label: {
                  display: true,
                  content: 'Now',
                  position: 'start',
                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                  color: 'white',
                  font: {
                    size: 12
                  }
                }
              }
            }
          },
          tooltip: {
            callbacks: {
              title: function(tooltipItems) {
                return tooltipItems[0].label;
              },
              label: function(context) {
                const power = context.raw;
                let status = 'Weak';
                if (power >= 80) status = 'Very Strong';
                else if (power >= 40) status = 'Strong';
                else if (power >= 20) status = 'Moderate';
                
                return `Power: ${power.toFixed(1)} GW (${status})`;
              }
            }
          }
        }
      }
    });
  }
  
  // Initialize aurora power chart
  async function initAuroraPowerChart() {
    const data = await fetchAuroraPowerData();
    
    if (data && data.length > 0) {
      // Format data for chart
      const timestamps = data.map(item => formatTime(parseISO(item.forecast)));
      const powerValues = data.map(item => item.northPower);
      
      createAuroraPowerChart(timestamps, powerValues);
    }
  }
  
  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    initAuroraPowerChart();
    
    // Listen for timezone changes and refresh chart
    addTimezoneChangeListener(() => {
      initAuroraPowerChart();
    });
    
    // Refresh data every 30 minutes
    setInterval(initAuroraPowerChart, 30 * 60 * 1000);
  });
</script>