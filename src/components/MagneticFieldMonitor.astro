---
---
<section id="magnetic-field" class="py-12 scroll-mt-20">
  <div class="container-custom">
    <div class="text-center mb-10">
      <h2 class="text-3xl font-bold mb-4">Magnetic Field Monitor</h2>
      <p class="text-gray-400 max-w-2xl mx-auto">
        Track solar wind and magnetic field data that directly influence aurora formation.
      </p>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Bz Component Chart -->
      <div class="card p-6">
        <h3 class="text-xl font-semibold mb-4">Magnetic Field (Bz)</h3>
        <div class="h-64 relative">
          <div id="bz-chart-loader" class="absolute inset-0 flex items-center justify-center bg-dark-800/80 backdrop-blur-sm z-10">
            <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-400"></div>
          </div>
          <canvas id="bz-chart"></canvas>
        </div>
        <div class="mt-4 p-4 bg-dark-700/50 rounded-lg">
          <p class="text-sm text-gray-300">
            <strong>Bz Component:</strong> A negative Bz (below 0) is favorable for aurora activity. Values below -10 nT typically trigger strong aurora displays.
          </p>
        </div>
      </div>
      
      <!-- Solar Wind Chart -->
      <div class="card p-6">
        <h3 class="text-xl font-semibold mb-4">Solar Wind Speed</h3>
        <div class="h-64 relative">
          <div id="wind-chart-loader" class="absolute inset-0 flex items-center justify-center bg-dark-800/80 backdrop-blur-sm z-10">
            <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-400"></div>
          </div>
          <canvas id="wind-chart"></canvas>
        </div>
        <div class="mt-4 p-4 bg-dark-700/50 rounded-lg">
          <p class="text-sm text-gray-300">
            <strong>Solar Wind Speed:</strong> Faster solar wind (above 500 km/s) increases the chance of aurora activity. Current estimated arrival time: <span id="arrival-time">calculating...</span>
          </p>
        </div>
      </div>
      
      <!-- Solar Activity Info -->
      <div class="card p-6 lg:col-span-2">
        <h3 class="text-xl font-semibold mb-4">Understanding Solar Activity & Aurora</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h4 class="font-medium mb-2">Magnetic Field</h4>
            <p class="text-sm text-gray-300">
              The Bz component of the interplanetary magnetic field (IMF) is crucial for aurora formation. When Bz is negative (southward), it connects with Earth's magnetic field, allowing solar particles to enter our atmosphere.
            </p>
          </div>
          <div>
            <h4 class="font-medium mb-2">Solar Wind</h4>
            <p class="text-sm text-gray-300">
              Solar wind is a stream of charged particles flowing from the Sun. Its speed, density, and magnetic field properties all influence aurora activity. Faster wind speeds typically result in stronger geomagnetic disturbances.
            </p>
          </div>
          <div>
            <h4 class="font-medium mb-2">Propagation Delay</h4>
            <p class="text-sm text-gray-300">
              Solar wind measurements are taken at the L1 point (about 1.5 million km from Earth). There's a delay between when we measure the solar wind and when it reaches Earth's magnetosphere, which we calculate based on current wind speed.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  import Chart from 'chart.js/auto';
  import { format } from 'date-fns';
  import { fetchMagneticField, fetchSolarWind } from '../utils/api.js';
  
  // Import timezone utilities
  import { formatTime, addTimezoneChangeListener } from '../utils/timezone.js';
  
  // Chart instances
  let bzChart = null;
  let windChart = null;
  
  // Calculate Earth arrival time for solar wind
  function calculateEarthTime(solarWindData) {
    const L1_DISTANCE = 1500000; // km
    if (!solarWindData || solarWindData.length === 0) return 'Unknown';
    
    const lastData = solarWindData[solarWindData.length - 1];
    const speed = lastData.speed;
    const timeAtL1 = new Date(lastData.time_tag);
    
    const travelTimeMinutes = (L1_DISTANCE / speed) / 60;
    const adjustedTime = new Date(timeAtL1.getTime() + travelTimeMinutes * 60000);
    
    return format(adjustedTime, 'MMM d, h:mm a');
  }
  
  // Create Bz chart
  function createBzChart(timestamps, bzValues) {
    const ctx = document.getElementById('bz-chart') as HTMLCanvasElement;
    
    if (bzChart) {
      bzChart.destroy();
    }
    
    bzChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: timestamps,
        datasets: [{
          label: 'Bz Component (nT)',
          data: bzValues,
          borderColor: '#2196F3',
          backgroundColor: 'rgba(33, 150, 243, 0.1)',
          borderWidth: 2,
          pointRadius: 2,
          fill: {
            target: 'origin',
            above: 'rgba(244, 67, 54, 0.1)',   // Red above zero
            below: 'rgba(33, 150,243, 0.2)'   // Blue below zero (good for aurora)
          }
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            ticks: {
              color: 'rgba(255, 255, 255, 0.7)'
            },
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            title: {
              display: true,
              text: 'Bz (nT)',
              color: 'rgba(255, 255, 255, 0.7)'
            }
          },
          x: {
            ticks: {
              color: 'rgba(255, 255, 255, 0.7)',
              maxRotation: 45,
              minRotation: 45
            },
            grid: {
              display: false
            }
          }
        },
        plugins: {
          tooltip: {
            callbacks: {
              afterLabel: function(context: any) {
                const value = context.raw;
                if (value < -10) return 'Strong aurora potential';
                if (value < 0) return 'Favorable for aurora';
                return 'Less favorable for aurora';
              }
            }
          }
        }
      }
    });
  }
  
  // Create solar wind chart
  function createWindChart(timestamps, speedValues) {
    const ctx = document.getElementById('wind-chart') as HTMLCanvasElement;
    
    if (windChart) {
      windChart.destroy();
    }
    
    // Generate gradient for background
    const gradient = ctx.getContext('2d')!.createLinearGradient(0, 0, 0, 300);
    gradient.addColorStop(0, 'rgba(255, 152, 0, 0.3)');    // Orange at top
    gradient.addColorStop(1, 'rgba(255, 152, 0, 0.05)');   // Fade to transparent
    
    windChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: timestamps,
        datasets: [{
          label: 'Solar Wind Speed (km/s)',
          data: speedValues,
          borderColor: '#FF9800',
          backgroundColor: gradient,
          borderWidth: 2,
          pointRadius: 2,
          fill: true,
          tension: 0.2
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: false,
            ticks: {
              color: 'rgba(255, 255, 255, 0.7)'
            },
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            title: {
              display: true,
              text: 'Speed (km/s)',
              color: 'rgba(255, 255, 255, 0.7)'
            }
          },
          x: {
            ticks: {
              color: 'rgba(255, 255, 255, 0.7)',
              maxRotation: 45,
              minRotation: 45
            },
            grid: {
              display: false
            }
          }
        },
        plugins: {
          tooltip: {
            callbacks: {
              afterLabel: function(context: any) {
                const value = context.raw;
                if (value > 700) return 'Very high (excellent for aurora)';
                if (value > 500) return 'High (good for aurora)';
                if (value > 400) return 'Moderate';
                return 'Low';
              }
            }
          }
        }
      }
    });
  }
  
  // Initialize magnetic field monitor
  async function initMagneticFieldMonitor() {
    try {
      // Show loaders
      const bzLoader = document.getElementById('bz-chart-loader');
      const windLoader = document.getElementById('wind-chart-loader');
      if (bzLoader) bzLoader.classList.remove('hidden');
      if (windLoader) windLoader.classList.remove('hidden');
      
      // Fetch and process magnetic field data
      const magData = await fetchMagneticField();
      if (magData && magData.length > 0) {
        // Format data for chart
        const timestamps = magData.map((item: any) => formatTime(new Date(item.time_tag)));
        const bzValues = magData.map((item: any) => item.bz_gsm);
        
        createBzChart(timestamps, bzValues);
      }
      
      // Fetch and process solar wind data
      const windData = await fetchSolarWind();
      if (windData && windData.length > 0) {
        // Format data for chart
        const timestamps = windData.map((item: any) => formatTime(new Date(item.time_tag)));
        const speedValues = windData.map((item: any) => item.speed);
        
        createWindChart(timestamps, speedValues);
        
        // Calculate and display arrival time
        const arrivalTime = calculateEarthTime(windData);
        const arrivalElement = document.getElementById('arrival-time');
        if (arrivalElement) {
          arrivalElement.textContent = arrivalTime;
        }
      }
      
      // Hide loaders
      if (bzLoader) bzLoader.classList.add('hidden');
      if (windLoader) windLoader.classList.add('hidden');
    } catch (error) {
      console.error('Error initializing magnetic field monitor:', error);
      // Hide loaders on error
      const bzLoader = document.getElementById('bz-chart-loader');
      const windLoader = document.getElementById('wind-chart-loader');
      if (bzLoader) bzLoader.classList.add('hidden');
      if (windLoader) windLoader.classList.add('hidden');
    }
  }
  
  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    initMagneticFieldMonitor();
    
    // Listen for timezone changes and refresh charts
    addTimezoneChangeListener(() => {
      initMagneticFieldMonitor();
    });
    
    // Refresh data every 20 minutes
    setInterval(initMagneticFieldMonitor, 20 * 60 * 1000);
  });
</script>