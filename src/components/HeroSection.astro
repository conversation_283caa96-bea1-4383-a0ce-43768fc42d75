---
---
<section class="py-12 md:py-20 relative overflow-hidden">
  <div class="absolute inset-0 bg-gradient-to-b from-primary-900/20 to-dark-900/0 z-0"></div>
  
  <div class="container-custom relative z-10">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
      <div>
        <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight">
          Track the <span class="gradient-text">Aurora</span> in Real-Time
        </h1>
        <p class="text-lg md:text-xl text-gray-300 mb-8 leading-relaxed">
          Get accurate forecasts, KP index monitoring, and alerts for the Northern and Southern Lights. Know exactly when and where to catch this spectacular natural phenomenon.
        </p>
        <div class="flex flex-wrap gap-4">
          <a href="#forecast" class="btn btn-primary">
            View Forecast
          </a>
          <a href="#kp-index" class="btn btn-outline">
            Check KP Index
          </a>
        </div>
      </div>
      
      <div class="relative">
        <div class="absolute inset-0 bg-primary-500/20 rounded-full blur-3xl"></div>
        <img 
          src="https://images.pexels.com/photos/1933239/pexels-photo-1933239.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" 
          alt="Aurora Borealis over a mountain landscape" 
          class="rounded-lg shadow-2xl relative z-10 w-full h-auto object-cover aspect-video"
          width="800"
          height="450"
        />
      </div>
    </div>
    
    <div class="mt-16 grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
      <div class="card p-4">
        <div class="text-2xl font-bold text-primary-300 mb-2" id="current-kp">--</div>
        <div class="text-sm text-gray-400">Current KP Index</div>
      </div>
      <div class="card p-4">
        <div class="text-2xl font-bold text-aurora-orange mb-2">24/7</div>
        <div class="text-sm text-gray-400">Real-time Updates</div>
      </div>
      <div class="card p-4">
        <div class="text-2xl font-bold text-aurora-green mb-2">NOAA</div>
        <div class="text-sm text-gray-400">Data Source</div>
      </div>
      <div class="card p-4">
        <div class="text-2xl font-bold text-aurora-blue mb-2">Global</div>
        <div class="text-sm text-gray-400">Coverage</div>
      </div>
    </div>
  </div>
</section>

<script>
  import { fetchKpIndex } from '../utils/api.js';
  
  // Fetch current KP index for the hero section
  async function fetchCurrentKpIndex() {
    try {
      const data = await fetchKpIndex();
      if (data && data.length > 0) {
        // Get the latest KP reading
        const latestKp = data[data.length - 1].kp;
        
        // Update the display
        const currentKpElement = document.getElementById('current-kp');
        if (currentKpElement) {
          currentKpElement.textContent = latestKp;
          
          // Add color class based on KP value
          currentKpElement.className = 'text-2xl font-bold mb-2';
          if (latestKp <= 3) {
            currentKpElement.classList.add('text-aurora-green');
          } else if (latestKp <= 5) {
            currentKpElement.classList.add('text-aurora-yellow');
          } else if (latestKp <= 7) {
            currentKpElement.classList.add('text-aurora-orange');
          } else {
            currentKpElement.classList.add('text-aurora-red');
          }
        }
      }
    } catch (error) {
      console.error('Error fetching KP index:', error);
    }
  }
  
  // Call the function when the component loads
  document.addEventListener('DOMContentLoaded', fetchCurrentKpIndex);
</script>