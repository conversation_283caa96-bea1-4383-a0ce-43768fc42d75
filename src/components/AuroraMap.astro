---
---
<section id="aurora-map" class="py-12 scroll-mt-20">
  <div class="container-custom">
    <div class="text-center mb-10">
      <h2 class="text-3xl font-bold mb-4">Aurora Visibility Map</h2>
      <p class="text-gray-400 max-w-2xl mx-auto">
        Real-time aurora probability map showing where the Northern and Southern Lights are most likely to appear.
      </p>
    </div>
    
    <div class="card p-6">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold">Aurora Probability</h3>
        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-400">
            <span id="observation-time">Loading...</span>
          </div>
          <button id="refresh-data" class="px-3 py-1 bg-primary-600 hover:bg-primary-700 rounded text-sm transition-colors">
            Refresh Data
          </button>
        </div>
      </div>
      
      <!-- Map Container -->
      <div id="aurora-map-container" class="h-96 rounded-lg overflow-hidden bg-dark-900 relative">
        <div id="map-loader" class="absolute inset-0 flex items-center justify-center bg-dark-800/80 backdrop-blur-sm z-10">
          <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-400"></div>
        </div>
        <div id="aurora-map-element" class="w-full h-full"></div>
      </div>
      
      <!-- Map Legend -->
      <div class="mt-6 p-4 bg-dark-700/50 rounded-lg">
        <h4 class="font-semibold mb-3">Aurora Intensity Legend</h4>

        <!-- Northern Aurora (Aurora Borealis) -->
        <div class="mb-4">
          <h5 class="text-sm font-medium text-cyan-300 mb-2">🌌 Aurora Borealis (Northern Hemisphere)</h5>
          <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
            <div class="flex items-center">
              <div class="w-4 h-4 rounded mr-2" style="background: rgba(0, 180, 80, 0.6);"></div>
              <span class="text-xs text-gray-300">Very Low (1-3)</span>
            </div>
            <div class="flex items-center">
              <div class="w-4 h-4 rounded mr-2" style="background: rgba(0, 255, 100, 0.7);"></div>
              <span class="text-xs text-gray-300">Low (4-6)</span>
            </div>
            <div class="flex items-center">
              <div class="w-4 h-4 rounded mr-2" style="background: rgba(0, 255, 150, 0.8);"></div>
              <span class="text-xs text-gray-300">Moderate (7-9)</span>
            </div>
            <div class="flex items-center">
              <div class="w-4 h-4 rounded mr-2" style="background: rgba(0, 200, 255, 0.8);"></div>
              <span class="text-xs text-gray-300">High (10-14)</span>
            </div>
            <div class="flex items-center">
              <div class="w-4 h-4 rounded mr-2" style="background: rgba(0, 255, 255, 0.9);"></div>
              <span class="text-xs text-gray-300">Very High (15+)</span>
            </div>
          </div>
        </div>

        <!-- Southern Aurora (Aurora Australis) -->
        <div>
          <h5 class="text-sm font-medium text-pink-300 mb-2">🌠 Aurora Australis (Southern Hemisphere)</h5>
          <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
            <div class="flex items-center">
              <div class="w-4 h-4 rounded mr-2" style="background: rgba(180, 100, 150, 0.6);"></div>
              <span class="text-xs text-gray-300">Very Low (1-3)</span>
            </div>
            <div class="flex items-center">
              <div class="w-4 h-4 rounded mr-2" style="background: rgba(255, 150, 150, 0.7);"></div>
              <span class="text-xs text-gray-300">Low (4-6)</span>
            </div>
            <div class="flex items-center">
              <div class="w-4 h-4 rounded mr-2" style="background: rgba(255, 100, 200, 0.8);"></div>
              <span class="text-xs text-gray-300">Moderate (7-9)</span>
            </div>
            <div class="flex items-center">
              <div class="w-4 h-4 rounded mr-2" style="background: rgba(200, 0, 255, 0.8);"></div>
              <span class="text-xs text-gray-300">High (10-14)</span>
            </div>
            <div class="flex items-center">
              <div class="w-4 h-4 rounded mr-2" style="background: rgba(255, 0, 255, 0.9);"></div>
              <span class="text-xs text-gray-300">Very High (15+)</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Map Information -->
      <div class="mt-6 p-4 bg-primary-900/30 rounded-lg">
        <h4 class="font-semibold mb-2">How to Use the Aurora Visibility Map:</h4>
        <ul class="text-sm text-gray-300 space-y-1">
          <li>• <strong>Toggle Layers:</strong> Use the layer control (top-right) to show/hide Northern or Southern aurora</li>
          <li>• <strong>Aurora Borealis (Northern):</strong> Green-blue colors represent northern hemisphere aurora activity</li>
          <li>• <strong>Aurora Australis (Southern):</strong> Purple-pink colors represent southern hemisphere aurora activity</li>
          <li>• <strong>Intensity Levels:</strong> Brighter colors indicate stronger aurora activity and better visibility</li>
          <li>• <strong>Best Viewing:</strong> High intensity areas (10+) offer the best viewing opportunities</li>
          <li>• <strong>Moderate Activity:</strong> Medium intensity areas (7-9) have moderate aurora activity</li>
          <li>• <strong>Weak Activity:</strong> Low intensity areas (1-6) may have weak aurora visible to cameras</li>
          <li>• <strong>Data Source:</strong> Real-time data from NOAA Space Weather Prediction Center OVATION model</li>
          <li>• <strong>Updates:</strong> Data refreshes automatically every 30 minutes</li>
        </ul>
      </div>
    </div>
  </div>
</section>

<script>
  // Import Leaflet dynamically to avoid SSR issues
  let L: any = null;
  let auroraMap: any = null;
  let northernLayer: any = null;
  let southernLayer: any = null;
  let layerControl: any = null;
  let auroraData: any = null;
  

  
  // Convert from NOAA's 0-360 system back to -180/180 for mapping
  function getCords180(long360: number, lat360: number): [number, number] {
    const lon180 = long360 > 180 ? long360 - 360 : long360;
    const lat180 = lat360 > 180 ? lat360 - 360 : lat360;
    return [lon180, lat180];
  }
  
  // Northern Aurora color mapping (Green-Blue spectrum for Aurora Borealis)
  function getNorthernAuroraColor(intensity: number): string {
    if (intensity >= 15) return 'rgba(0, 255, 255, 0.9)';      // Bright cyan - Very High (15+)
    if (intensity >= 10) return 'rgba(0, 200, 255, 0.8)';      // Light blue - High (10-14)
    if (intensity >= 7) return 'rgba(0, 255, 150, 0.8)';       // Green-cyan - Moderate (7-9)
    if (intensity >= 4) return 'rgba(0, 255, 100, 0.7)';       // Bright green - Low (4-6)
    if (intensity >= 1) return 'rgba(0, 180, 80, 0.6)';        // Forest green - Very low (1-3)
    return 'rgba(0, 0, 0, 0)';                                 // Transparent - No activity (0)
  }

  // Southern Aurora color mapping (Purple-Pink spectrum for Aurora Australis)
  function getSouthernAuroraColor(intensity: number): string {
    if (intensity >= 15) return 'rgba(255, 0, 255, 0.9)';      // Bright magenta - Very High (15+)
    if (intensity >= 10) return 'rgba(200, 0, 255, 0.8)';      // Purple - High (10-14)
    if (intensity >= 7) return 'rgba(255, 100, 200, 0.8)';     // Pink-purple - Moderate (7-9)
    if (intensity >= 4) return 'rgba(255, 150, 150, 0.7)';     // Light pink - Low (4-6)
    if (intensity >= 1) return 'rgba(180, 100, 150, 0.6)';     // Muted pink - Very low (1-3)
    return 'rgba(0, 0, 0, 0)';                                 // Transparent - No activity (0)
  }

  // Get appropriate color based on hemisphere
  function getAuroraColor(intensity: number, hemisphere: 'northern' | 'southern'): string {
    return hemisphere === 'northern'
      ? getNorthernAuroraColor(intensity)
      : getSouthernAuroraColor(intensity);
  }
  
  // Get radius based on aurora intensity with hemisphere-specific scaling
  function getRadiusForIntensity(intensity: number, hemisphere: 'northern' | 'southern'): number {
    // Base radius calculation
    let baseRadius = 0;
    if (intensity >= 15) baseRadius = 150000; // 150km for very high intensity
    else if (intensity >= 10) baseRadius = 110000;  // 110km for high
    else if (intensity >= 7) baseRadius = 80000;   // 80km for moderate
    else if (intensity >= 4) baseRadius = 60000;   // 60km for low
    else if (intensity >= 1) baseRadius = 40000;   // 40km for very low

    // Slightly larger circles for southern hemisphere due to typically lower activity
    const hemisphereMultiplier = hemisphere === 'southern' ? 1.2 : 1.0;

    return Math.round(baseRadius * hemisphereMultiplier);
  }

  // Get enhanced visual properties for aurora zones
  function getZoneProperties(intensity: number, hemisphere: 'northern' | 'southern') {
    const baseColor = getAuroraColor(intensity, hemisphere);
    const radius = getRadiusForIntensity(intensity, hemisphere);

    // Enhanced visual properties based on intensity
    let fillOpacity = 0.4;
    let strokeOpacity = 0.7;
    let strokeWeight = 1;

    if (intensity >= 15) {
      fillOpacity = 0.7;
      strokeOpacity = 0.9;
      strokeWeight = 2;
    } else if (intensity >= 10) {
      fillOpacity = 0.6;
      strokeOpacity = 0.8;
      strokeWeight = 2;
    } else if (intensity >= 7) {
      fillOpacity = 0.5;
      strokeOpacity = 0.7;
      strokeWeight = 1;
    }

    return {
      color: baseColor,
      fillColor: baseColor,
      fillOpacity,
      opacity: strokeOpacity,
      weight: strokeWeight,
      radius
    };
  }
  
  // Fetch aurora data from NOAA API
  async function fetchAuroraData(): Promise<any> {
    try {
      const response = await fetch('https://services.swpc.noaa.gov/json/ovation_aurora_latest.json');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      
      // Update observation time - try both possible field names
      const observationTime = document.getElementById('observation-time');
      if (observationTime) {
        const timeField = data['Observation Time'] || data['Forecast Time'];
        if (timeField) {
          const date = new Date(timeField);
          observationTime.textContent = `Updated: ${date.toLocaleString()}`;
        }
      }
      
      return data;
    } catch (error) {
      console.error('Error fetching aurora data:', error);
      
      // Update UI to show error
      const observationTime = document.getElementById('observation-time');
      if (observationTime) {
        observationTime.textContent = 'Data unavailable';
      }
      
      return null;
    }
  }
  
  // Filter and process aurora data, separating by hemisphere
  function processAuroraData(data: any) {
    if (!data || !data.coordinates) return { northern: [], southern: [] };

    const northernData = [];
    const southernData = [];
    const coordinates = data.coordinates;

    // Process each coordinate point
    for (const coord of coordinates) {
      const [longitude360, latitude360, intensity] = coord;

      // Convert from NOAA's 0-360 system to standard -180/180 for mapping
      const [longitude, latitude] = getCords180(longitude360, latitude360);

      // Only include points with aurora activity (threshold can be adjusted)
      if (intensity > 0) {
        const point = {
          longitude,
          latitude,
          intensity: intensity
        };

        // Separate by hemisphere
        if (latitude >= 0) {
          northernData.push(point);
        } else {
          southernData.push(point);
        }
      }
    }

    return {
      northern: northernData,
      southern: southernData
    };
  }
  
  // Initialize the map
  async function initAuroraMap() {
    try {
      // Load Leaflet
      L = await import('leaflet');
      
      const mapElement = document.getElementById('aurora-map-element');
      const loader = document.getElementById('map-loader');
      
      if (!mapElement) return;
      
      // Initialize map
      auroraMap = L.map(mapElement, {
        center: [65, -100],
        zoom: 3,
        minZoom: 2,
        maxZoom: 10
      });
      
      // Add dark tile layer
      L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
        subdomains: 'abcd',
        maxZoom: 19
      }).addTo(auroraMap);
      
      // Load aurora data
      await loadAuroraData();
      
      // Hide loader
      if (loader) loader.classList.add('hidden');
      
    } catch (error) {
      console.error('Error initializing aurora map:', error);
      const loader = document.getElementById('map-loader');
      if (loader) loader.classList.add('hidden');
    }
  }
  
  // Create aurora layer for a specific hemisphere
  function createAuroraLayer(hemisphereData: any[], hemisphere: 'northern' | 'southern') {
    const circles: any[] = [];

    // Group points by intensity for better visualization
    const intensityGroups = new Map();
    for (const point of hemisphereData) {
      const intensityLevel = Math.floor(point.intensity);
      if (!intensityGroups.has(intensityLevel)) {
        intensityGroups.set(intensityLevel, []);
      }
      intensityGroups.get(intensityLevel).push(point);
    }

    // Create circles for each point with enhanced properties
    for (const point of hemisphereData) {
      const { longitude, latitude, intensity } = point;

      const zoneProps = getZoneProperties(intensity, hemisphere);
      if (zoneProps.radius > 0) {
        const circle = L.circle([latitude, longitude], zoneProps).bindTooltip(
          `<div style="font-family: system-ui, sans-serif;">
            <div style="font-weight: bold; color: ${hemisphere === 'northern' ? '#00ffff' : '#ff00ff'}; margin-bottom: 4px;">
              ${hemisphere === 'northern' ? '🌌 Aurora Borealis' : '🌠 Aurora Australis'}
            </div>
            <div><strong>Intensity:</strong> ${intensity.toFixed(1)}</div>
            <div><strong>Location:</strong> ${latitude.toFixed(2)}°, ${longitude.toFixed(2)}°</div>
            <div style="margin-top: 4px; font-size: 12px; color: #888;">
              ${getIntensityDescription(intensity)}
            </div>
          </div>`,
          {
            permanent: false,
            direction: 'top',
            offset: [0, -10],
            className: 'aurora-tooltip'
          }
        );

        circles.push(circle);
      }
    }

    return circles.length > 0 ? L.layerGroup(circles) : null;
  }

  // Get intensity description for tooltips
  function getIntensityDescription(intensity: number): string {
    if (intensity >= 15) return 'Excellent viewing conditions - Very bright aurora expected';
    if (intensity >= 10) return 'Great viewing conditions - Bright aurora likely';
    if (intensity >= 7) return 'Good viewing conditions - Moderate aurora activity';
    if (intensity >= 4) return 'Fair viewing conditions - Weak aurora possible';
    if (intensity >= 1) return 'Poor viewing conditions - Very faint aurora';
    return 'No aurora activity detected';
  }

  // Load and display aurora data
  async function loadAuroraData() {
    if (!auroraMap || !L) return;

    try {
      // Show loader
      const loader = document.getElementById('map-loader');
      if (loader) loader.classList.remove('hidden');

      // Fetch fresh data
      auroraData = await fetchAuroraData();

      if (!auroraData) {
        console.warn('No aurora data available');
        return;
      }

      // Clear existing layers from map and layer control
      if (northernLayer) {
        auroraMap.removeLayer(northernLayer);
        if (layerControl) {
          layerControl.removeLayer(northernLayer);
        }
      }
      if (southernLayer) {
        auroraMap.removeLayer(southernLayer);
        if (layerControl) {
          layerControl.removeLayer(southernLayer);
        }
      }

      // Process the data by hemisphere
      const hemisphereData = processAuroraData(auroraData);
      console.log(`Loaded ${hemisphereData.northern.length} northern and ${hemisphereData.southern.length} southern aurora data points`);

      // Create hemisphere layers
      northernLayer = createAuroraLayer(hemisphereData.northern, 'northern');
      southernLayer = createAuroraLayer(hemisphereData.southern, 'southern');

      // Setup layer controls if not already done
      if (!layerControl) {
        setupLayerControls();
      }

      // Add layers to layer control and map
      if (northernLayer) {
        layerControl.addOverlay(northernLayer, '🌌 Aurora Borealis (Northern)');
        northernLayer.addTo(auroraMap);
      }

      if (southernLayer) {
        layerControl.addOverlay(southernLayer, '🌠 Aurora Australis (Southern)');
        southernLayer.addTo(auroraMap);
      }

      // Fit map to show all aurora activity
      const allLayers = [];
      if (northernLayer) allLayers.push(northernLayer);
      if (southernLayer) allLayers.push(southernLayer);

      if (allLayers.length > 0) {
        const group = new L.featureGroup(allLayers.flatMap(layer => layer.getLayers()));
        auroraMap.fitBounds(group.getBounds(), { padding: [20, 20] });
      } else {
        // No aurora activity, show default view
        auroraMap.setView([65, -100], 3);
      }

    } catch (error) {
      console.error('Error loading aurora data:', error);
    } finally {
      // Hide loader
      const loader = document.getElementById('map-loader');
      if (loader) loader.classList.add('hidden');
    }
  }

  // Setup layer controls
  function setupLayerControls() {
    if (!L || !auroraMap) return;

    // Create layer control
    layerControl = L.control.layers({}, {}, {
      position: 'topright',
      collapsed: false
    });

    layerControl.addTo(auroraMap);
  }
  
  // Setup event listeners
  function setupEventListeners() {
    const refreshBtn = document.getElementById('refresh-data');
    
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        loadAuroraData();
      });
    }
    
    // Auto-refresh every 30 minutes
    setInterval(() => {
      loadAuroraData();
    }, 30 * 60 * 1000);
  }
  
  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    setupEventListeners();
    initAuroraMap();
  });
</script>

<style>
  /* Refresh button animation */
  #refresh-data:hover {
    transform: scale(1.05);
  }

  #refresh-data:active {
    transform: scale(0.95);
  }

  /* Loading animation for the map */
  #map-loader {
    transition: opacity 0.3s ease-in-out;
  }

  #map-loader.hidden {
    opacity: 0;
    pointer-events: none;
  }

  /* Leaflet layer control styling */
  :global(.leaflet-control-layers) {
    background: rgba(31, 41, 55, 0.95) !important;
    border: 1px solid rgba(75, 85, 99, 0.5) !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    backdrop-filter: blur(8px);
  }

  :global(.leaflet-control-layers-toggle) {
    background-color: rgba(31, 41, 55, 0.95) !important;
    border-radius: 6px !important;
  }

  :global(.leaflet-control-layers label) {
    color: #e5e7eb !important;
    font-size: 14px !important;
    margin: 4px 0 !important;
  }

  :global(.leaflet-control-layers input[type="checkbox"]) {
    margin-right: 8px !important;
  }

  :global(.leaflet-control-layers-separator) {
    border-top: 1px solid rgba(75, 85, 99, 0.5) !important;
    margin: 8px 0 !important;
  }

  /* Custom styling for hemisphere labels */
  :global(.leaflet-control-layers label:has(input[type="checkbox"]:checked)) {
    color: #60a5fa !important;
    font-weight: 500 !important;
  }

  /* Enhanced tooltip styling */
  :global(.aurora-tooltip) {
    background: rgba(17, 24, 39, 0.95) !important;
    border: 1px solid rgba(75, 85, 99, 0.5) !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    backdrop-filter: blur(8px);
    color: #f3f4f6 !important;
    font-size: 13px !important;
    padding: 8px 12px !important;
    max-width: 250px !important;
  }

  :global(.aurora-tooltip::before) {
    border-top-color: rgba(17, 24, 39, 0.95) !important;
  }

  /* Aurora circle hover effects */
  :global(.leaflet-interactive:hover) {
    filter: brightness(1.2) !important;
    transition: filter 0.2s ease-in-out !important;
  }
</style>