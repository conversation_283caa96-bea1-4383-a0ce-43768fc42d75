---
---
<section id="aurora-map" class="py-12 scroll-mt-20">
  <div class="container-custom">
    <div class="text-center mb-10">
      <h2 class="text-3xl font-bold mb-4">Aurora Visibility Map</h2>
      <p class="text-gray-400 max-w-2xl mx-auto">
        Real-time aurora probability map showing where the Northern and Southern Lights are most likely to appear.
      </p>
    </div>
    
    <div class="card p-6">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold">Aurora Probability</h3>
        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-400">
            <span id="observation-time">Loading...</span>
          </div>
          <button id="refresh-data" class="px-3 py-1 bg-primary-600 hover:bg-primary-700 rounded text-sm transition-colors">
            Refresh Data
          </button>
        </div>
      </div>
      
      <!-- Map Container -->
      <div id="aurora-map-container" class="h-96 rounded-lg overflow-hidden bg-dark-900 relative">
        <div id="map-loader" class="absolute inset-0 flex items-center justify-center bg-dark-800/80 backdrop-blur-sm z-10">
          <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-400"></div>
        </div>
        <div id="aurora-map-element" class="w-full h-full"></div>
      </div>
      
      <!-- Map Legend -->
      <div class="mt-6 p-4 bg-dark-700/50 rounded-lg">
        <h4 class="font-semibold mb-3">Aurora Intensity Legend</h4>
        <div class="grid grid-cols-2 md:grid-cols-6 gap-4">
          <div class="flex items-center">
            <div class="w-4 h-4 rounded mr-2" style="background: rgba(0, 0, 0, 0);"></div>
            <span class="text-sm text-gray-300">No Activity (0)</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 rounded mr-2" style="background: rgba(0, 128, 0, 0.6);"></div>
            <span class="text-sm text-gray-300">Very Low (1-3)</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 rounded mr-2" style="background: rgba(0, 255, 0, 0.7);"></div>
            <span class="text-sm text-gray-300">Low (4-6)</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 rounded mr-2" style="background: rgba(255, 255, 0, 0.8);"></div>
            <span class="text-sm text-gray-300">Moderate (7-9)</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 rounded mr-2" style="background: rgba(255, 128, 0, 0.8);"></div>
            <span class="text-sm text-gray-300">High (10-14)</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 rounded mr-2" style="background: rgba(255, 0, 0, 0.9);"></div>
            <span class="text-sm text-gray-300">Very High (15+)</span>
          </div>
        </div>
      </div>
      
      <!-- Map Information -->
      <div class="mt-6 p-4 bg-primary-900/30 rounded-lg">
        <h4 class="font-semibold mb-2">How to Use the Map:</h4>
        <ul class="text-sm text-gray-300 space-y-1">
          <li>• Colored regions show aurora intensity levels based on NOAA OVATION model</li>
          <li>• Higher values indicate stronger aurora activity and better visibility</li>
          <li>• Red and orange areas (10+) offer the best viewing opportunities</li>
          <li>• Yellow areas (7-9) have moderate aurora activity</li>
          <li>• Green areas (1-6) may have weak aurora visible to cameras</li>
          <li>• Data updates automatically from NOAA Space Weather Prediction Center</li>
        </ul>
      </div>
    </div>
  </div>
</section>

<script>
  // Import Leaflet dynamically to avoid SSR issues
  let L: any = null;
  let auroraMap: any = null;
  let currentLayer: any = null;
  let auroraData: any = null;
  
  // Convert from -180/180 coordinate system to NOAA's 0-360 system
  // Formula from: http://www.idlcoyote.com/map_tips/lonconvert.html
  function getCords360(long180: number, lat180: number): [number, number] {
    const lon360 = (long180 + 360) % 360;
    const lat360 = (lat180 + 360) % 360;
    return [Math.round(lon360), Math.round(lat360)];
  }
  
  // Convert from NOAA's 0-360 system back to -180/180 for mapping
  function getCords180(long360: number, lat360: number): [number, number] {
    const lon180 = long360 > 180 ? long360 - 360 : long360;
    const lat180 = lat360 > 180 ? lat360 - 360 : lat360;
    return [lon180, lat180];
  }
  
  // Aurora intensity color mapping (NOAA scale appears to be 0-22+)
  function getAuroraColor(intensity: number): string {
    if (intensity >= 15) return 'rgba(255, 0, 0, 0.9)';        // Red - Very High (15+)
    if (intensity >= 10) return 'rgba(255, 128, 0, 0.8)';      // Orange - High (10-14)
    if (intensity >= 7) return 'rgba(255, 255, 0, 0.8)';       // Yellow - Moderate (7-9)
    if (intensity >= 4) return 'rgba(0, 255, 0, 0.7)';         // Green - Low (4-6)
    if (intensity >= 1) return 'rgba(0, 128, 0, 0.6)';         // Dark green - Very low (1-3)
    return 'rgba(0, 0, 0, 0)';                                 // Transparent - No activity (0)
  }
  
  // Get radius based on aurora intensity
  function getRadiusForIntensity(intensity: number): number {
    if (intensity >= 15) return 120000; // 120km for very high intensity
    if (intensity >= 10) return 90000;  // 90km for high
    if (intensity >= 7) return 70000;   // 70km for moderate
    if (intensity >= 4) return 50000;   // 50km for low
    if (intensity >= 1) return 30000;   // 30km for very low
    return 0;                           // No radius for no activity
  }
  
  // Fetch aurora data from NOAA API
  async function fetchAuroraData(): Promise<any> {
    try {
      const response = await fetch('https://services.swpc.noaa.gov/json/ovation_aurora_latest.json');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      
      // Update observation time - try both possible field names
      const observationTime = document.getElementById('observation-time');
      if (observationTime) {
        const timeField = data['Observation Time'] || data['Forecast Time'];
        if (timeField) {
          const date = new Date(timeField);
          observationTime.textContent = `Updated: ${date.toLocaleString()}`;
        }
      }
      
      return data;
    } catch (error) {
      console.error('Error fetching aurora data:', error);
      
      // Update UI to show error
      const observationTime = document.getElementById('observation-time');
      if (observationTime) {
        observationTime.textContent = 'Data unavailable';
      }
      
      return null;
    }
  }
  
  // Filter and process aurora data
  function processAuroraData(data: any) {
    if (!data || !data.coordinates) return [];
    
    const processedData = [];
    const coordinates = data.coordinates;
    
    // Process each coordinate point
    for (const coord of coordinates) {
      const [longitude360, latitude360, intensity] = coord;
      
      // Convert from NOAA's 0-360 system to standard -180/180 for mapping
      const [longitude, latitude] = getCords180(longitude360, latitude360);
      
      // Only include points with aurora activity (threshold can be adjusted)
      if (intensity > 0) {
        processedData.push({
          longitude,
          latitude,
          intensity: intensity
        });
      }
    }
    
    return processedData;
  }
  
  // Initialize the map
  async function initAuroraMap() {
    try {
      // Load Leaflet
      L = await import('leaflet');
      
      const mapElement = document.getElementById('aurora-map-element');
      const loader = document.getElementById('map-loader');
      
      if (!mapElement) return;
      
      // Initialize map
      auroraMap = L.map(mapElement, {
        center: [65, -100],
        zoom: 3,
        minZoom: 2,
        maxZoom: 10
      });
      
      // Add dark tile layer
      L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
        subdomains: 'abcd',
        maxZoom: 19
      }).addTo(auroraMap);
      
      // Load aurora data
      await loadAuroraData();
      
      // Hide loader
      if (loader) loader.classList.add('hidden');
      
    } catch (error) {
      console.error('Error initializing aurora map:', error);
      const loader = document.getElementById('map-loader');
      if (loader) loader.classList.add('hidden');
    }
  }
  
  // Load and display aurora data
  async function loadAuroraData() {
    if (!auroraMap || !L) return;
    
    try {
      // Show loader
      const loader = document.getElementById('map-loader');
      if (loader) loader.classList.remove('hidden');
      
      // Fetch fresh data
      auroraData = await fetchAuroraData();
      
      if (!auroraData) {
        console.warn('No aurora data available');
        return;
      }
      
      // Clear existing layer
      if (currentLayer) {
        auroraMap.removeLayer(currentLayer);
      }
      
      // Process the data
      const processedData = processAuroraData(auroraData);
      console.log(`Loaded ${processedData.length} aurora data points`);
      
      const circles: any[] = [];
      
      // Create circles for each data point
      for (const point of processedData) {
        const { longitude, latitude, intensity } = point;
        
        const radius = getRadiusForIntensity(intensity);
        if (radius > 0) {
          const circle = L.circle([latitude, longitude], {
            color: getAuroraColor(intensity),
            fillColor: getAuroraColor(intensity),
            fillOpacity: 0.6,
            radius: radius,
            weight: 1,
            opacity: 0.8
          }).bindTooltip(
            `<div>
              <strong>Aurora Intensity: ${intensity}</strong><br>
              Latitude: ${latitude.toFixed(2)}°<br>
              Longitude: ${longitude.toFixed(2)}°
            </div>`,
            { 
              permanent: false,
              direction: 'top',
              offset: [0, -10]
            }
          );
          
          circles.push(circle);
        }
      }
      
      // Add layer to map
      if (circles.length > 0) {
        currentLayer = L.layerGroup(circles);
        currentLayer.addTo(auroraMap);
        
        // Fit map to show all aurora activity
        const group = new L.featureGroup(circles);
        auroraMap.fitBounds(group.getBounds(), { padding: [20, 20] });
      } else {
        // No aurora activity, show default view
        auroraMap.setView([65, -100], 3);
      }
      
    } catch (error) {
      console.error('Error loading aurora data:', error);
    } finally {
      // Hide loader
      const loader = document.getElementById('map-loader');
      if (loader) loader.classList.add('hidden');
    }
  }
  
  // Setup event listeners
  function setupEventListeners() {
    const refreshBtn = document.getElementById('refresh-data');
    
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        loadAuroraData();
      });
    }
    
    // Auto-refresh every 30 minutes
    setInterval(() => {
      loadAuroraData();
    }, 30 * 60 * 1000);
  }
  
  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    setupEventListeners();
    initAuroraMap();
  });
</script>

<style>
  /* Refresh button animation */
  #refresh-data:hover {
    transform: scale(1.05);
  }
  
  #refresh-data:active {
    transform: scale(0.95);
  }
  
  /* Loading animation for the map */
  #map-loader {
    transition: opacity 0.3s ease-in-out;
  }
  
  #map-loader.hidden {
    opacity: 0;
    pointer-events: none;
  }
</style> 