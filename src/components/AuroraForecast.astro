---
---
<section id="forecast" class="py-12 scroll-mt-20">
  <div class="container-custom">
    <div class="text-center mb-10">
      <h2 class="text-3xl font-bold mb-4">Aurora Forecast</h2>
      <p class="text-gray-400 max-w-2xl mx-auto">
        View the latest aurora forecast images from NOAA's Space Weather Prediction Center, updated in real-time.
      </p>
    </div>
    
    <div class="card p-6">
      <!-- Tabs -->
      <div class="overflow-x-auto hide-scrollbar mb-6">
        <div class="flex space-x-2 min-w-max">
          <button class="tab active" data-target="north30min">Northern Hemisphere (30 min)</button>
          <button class="tab" data-target="south30min">Southern Hemisphere (30 min)</button>
          <button class="tab" data-target="tonightNA">Tonight (North America)</button>
          <button class="tab" data-target="tomorrowNA">Tomorrow (North America)</button>
        </div>
      </div>
      
      <!-- Image container -->
      <div class="relative min-h-[300px] md:min-h-[500px] overflow-hidden rounded-lg bg-dark-900">
        <div id="image-loader" class="absolute inset-0 flex items-center justify-center bg-dark-900">
          <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-400"></div>
        </div>
        
        <!-- Image panels -->
        <div id="north30min" class="forecast-panel active">
          <img src="" alt="Northern Hemisphere 30-minute Aurora Forecast" class="w-full h-auto cursor-zoom-in" loading="lazy" onclick="openFullscreen(this)" />
        </div>
        
        <div id="south30min" class="forecast-panel hidden">
          <img src="" alt="Southern Hemisphere 30-minute Aurora Forecast" class="w-full h-auto cursor-zoom-in" loading="lazy" onclick="openFullscreen(this)" />
        </div>
        
        <div id="tonightNA" class="forecast-panel hidden">
          <img src="" alt="Tonight's Aurora Forecast for North America" class="w-full h-auto cursor-zoom-in" loading="lazy" onclick="openFullscreen(this)" />
        </div>
        
        <div id="tomorrowNA" class="forecast-panel hidden">
          <img src="" alt="Tomorrow's Aurora Forecast for North America" class="w-full h-auto cursor-zoom-in" loading="lazy" onclick="openFullscreen(this)" />
        </div>
      </div>
      
      <div class="mt-4 text-sm text-gray-400">
        <p>Last updated: <span id="last-updated">Loading...</span></p>
        <p class="mt-1">Source: NOAA Space Weather Prediction Center</p>
      </div>
      
      <div class="mt-6 p-4 bg-primary-900/30 rounded-lg">
        <h3 class="font-semibold mb-2">How to read aurora forecast maps:</h3>
        <p class="text-sm text-gray-300">
          These maps show the predicted aurora activity. The brighter colors (red, orange) indicate higher probability and intensity of aurora visibility. The green line represents the approximate southern limit of visibility for northern hemisphere, or northern limit for southern hemisphere.
        </p>
      </div>
    </div>
  </div>
</section>

<!-- Fullscreen Modal -->
<div id="fullscreen-modal" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden flex items-center justify-center">
  <div class="relative w-full h-full flex items-center justify-center p-4">
    <img id="fullscreen-image" src="" alt="" class="max-w-full max-h-full object-contain" />
    <button 
      onclick="closeFullscreen()" 
      class="absolute top-4 right-4 text-white text-4xl hover:text-gray-300 transition-colors"
      aria-label="Close fullscreen"
    >
      ×
    </button>
  </div>
</div>

<script>
  // Aurora image sources
  const auroraImageSources = {
    north30min: "https://services.swpc.noaa.gov/images/animations/ovation/north/latest.jpg",
    south30min: "https://services.swpc.noaa.gov/images/animations/ovation/south/latest.jpg", 
    tonightNA: "https://services.swpc.noaa.gov/experimental/images/aurora_dashboard/tonights_static_viewline_forecast.png",
    tomorrowNA: "https://services.swpc.noaa.gov/experimental/images/aurora_dashboard/tomorrow_nights_static_viewline_forecast.png"
  };
  
  // Add timestamp to prevent caching
  function getImageUrlWithTimestamp(baseUrl) {
    return `${baseUrl}?${Date.now()}`;
  }
  
  // Update the last updated timestamp
  function updateTimestamp() {
    const now = new Date();
    const formattedDate = now.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
    document.getElementById('last-updated').textContent = formattedDate;
  }
  
  // Load images with timestamps
  function loadImages() {
    const loader = document.getElementById('image-loader');
    loader.classList.remove('hidden');
    
    // Load all images
    Object.entries(auroraImageSources).forEach(([key, url]) => {
      const imgElement = document.querySelector(`#${key} img`);
      if (imgElement) {
        const imgWithTimestamp = getImageUrlWithTimestamp(url);
        imgElement.src = imgWithTimestamp;
        
        // Hide loader when the first active image is loaded
        if (document.getElementById(key).classList.contains('active')) {
          imgElement.onload = () => {
            loader.classList.add('hidden');
          };
          
          imgElement.onerror = () => {
            loader.classList.add('hidden');
            imgElement.alt = "Image failed to load. Please try again later.";
          };
        }
      }
    });
    
    updateTimestamp();
  }
  
  // Tab switching functionality
  function setupTabs() {
    const tabs = document.querySelectorAll('.tab');
    const panels = document.querySelectorAll('.forecast-panel');
    const loader = document.getElementById('image-loader');
    
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        // Show loader
        loader.classList.remove('hidden');
        
        // Remove active class from all tabs
        tabs.forEach(t => t.classList.remove('active'));
        
        // Add active class to clicked tab
        tab.classList.add('active');
        
        // Hide all panels
        panels.forEach(panel => panel.classList.add('hidden'));
        panels.forEach(panel => panel.classList.remove('active'));
        
        // Show selected panel
        const targetId = tab.getAttribute('data-target');
        const targetPanel = document.getElementById(targetId);
        if (targetPanel) {
          targetPanel.classList.remove('hidden');
          targetPanel.classList.add('active');
          
          // Hide loader when image is loaded
          const img = targetPanel.querySelector('img');
          if (img.complete) {
            loader.classList.add('hidden');
          } else {
            img.onload = () => loader.classList.add('hidden');
            img.onerror = () => {
              loader.classList.add('hidden');
              img.alt = "Image failed to load. Please try again later.";
            };
          }
        }
      });
    });
  }
  
  // Fullscreen functionality
  function openFullscreen(img) {
    const modal = document.getElementById('fullscreen-modal');
    const fullscreenImg = document.getElementById('fullscreen-image');
    
    if (modal && fullscreenImg && img) {
      fullscreenImg.src = img.src;
      fullscreenImg.alt = img.alt;
      modal.classList.remove('hidden');
      document.body.style.overflow = 'hidden';
    }
  }
  
  function closeFullscreen() {
    const modal = document.getElementById('fullscreen-modal');
    
    if (modal) {
      modal.classList.add('hidden');
      document.body.style.overflow = 'auto';
    }
  }
  
  // Close fullscreen on escape key
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      closeFullscreen();
    }
  });
  
  // Close fullscreen on background click
  document.getElementById('fullscreen-modal')?.addEventListener('click', (e) => {
    if (e.target === e.currentTarget) {
      closeFullscreen();
    }
  });

  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    loadImages();
    setupTabs();
    
    // Refresh images every 15 minutes
    setInterval(loadImages, 15 * 60 * 1000);
  });
</script>

<style>
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .forecast-panel img {
    max-height: 70vh;
    margin: 0 auto;
    object-fit: contain;
  }
</style>