---
const currentYear = new Date().getFullYear();
---

<footer class="bg-dark-900 border-t border-dark-700 py-8">
  <div class="container-custom">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div>
        <a href="/" class="flex items-center gap-2 mb-4">
          <span class="w-6 h-6">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="url(#paint0_linear)"/>
              <path d="M15 9C15 10.6569 13.6569 12 12 12C10.3431 12 9 10.6569 9 9C9 7.34315 10.3431 6 12 6C13.6569 6 15 7.34315 15 9Z" fill="url(#paint1_linear)"/>
              <path d="M8.5 15.5C8.5 16.3284 9.17157 17 10 17C10.8284 17 11.5 16.3284 11.5 15.5C11.5 14.6716 10.8284 14 10 14C9.17157 14 8.5 14.6716 8.5 15.5Z" fill="url(#paint1_linear)"/>
              <path d="M12.5 15.5C12.5 16.3284 13.1716 17 14 17C14.8284 17 15.5 16.3284 15.5 15.5C15.5 14.6716 14.8284 14 14 14C13.1716 14 12.5 14.6716 12.5 15.5Z" fill="url(#paint1_linear)"/>
              <defs>
                <linearGradient id="paint0_linear" x1="2" y1="12" x2="22" y2="12" gradientUnits="userSpaceOnUse">
                  <stop stop-color="#4CAF50"/>
                  <stop offset="0.5" stop-color="#9C27B0"/>
                  <stop offset="1" stop-color="#2196F3"/>
                </linearGradient>
                <linearGradient id="paint1_linear" x1="9" y1="9" x2="15" y2="9" gradientUnits="userSpaceOnUse">
                  <stop stop-color="#4CAF50"/>
                  <stop offset="0.5" stop-color="#9C27B0"/>
                  <stop offset="1" stop-color="#2196F3"/>
                </linearGradient>
              </defs>
            </svg>
          </span>
          <span class="text-lg font-display font-bold">AuroraAlert</span>
        </a>
        <p class="text-sm text-gray-400 mb-4">
          Real-time aurora forecasts and alerts to help you catch the northern and southern lights.
        </p>
        <div class="flex space-x-4">
          <a href="#" aria-label="Twitter" class="text-gray-400 hover:text-primary-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
            </svg>
          </a>
          <a href="#" aria-label="Facebook" class="text-gray-400 hover:text-primary-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
            </svg>
          </a>
          <a href="#" aria-label="Instagram" class="text-gray-400 hover:text-primary-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
            </svg>
          </a>
        </div>
      </div>
      
      <div>
        <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
        <ul class="space-y-2">
          <li><a href="#forecast" class="text-gray-400 hover:text-primary-300 transition-colors">Aurora Forecast</a></li>
          <li><a href="#kp-index" class="text-gray-400 hover:text-primary-300 transition-colors">KP Index</a></li>
          <li><a href="#aurora-power" class="text-gray-400 hover:text-primary-300 transition-colors">Aurora Power</a></li>
          <li><a href="#magnetic-field" class="text-gray-400 hover:text-primary-300 transition-colors">Magnetic Field Data</a></li>
        </ul>
      </div>
      
      <div>
        <h3 class="text-lg font-semibold mb-4">Information</h3>
        <ul class="space-y-2">
          <li><a href="#" class="text-gray-400 hover:text-primary-300 transition-colors">About Aurora</a></li>
          <li><a href="#" class="text-gray-400 hover:text-primary-300 transition-colors">How to View</a></li>
          <li><a href="#" class="text-gray-400 hover:text-primary-300 transition-colors">Photography Tips</a></li>
          <li><a href="#" class="text-gray-400 hover:text-primary-300 transition-colors">Contact Us</a></li>
        </ul>
      </div>
    </div>
    
    <div class="mt-8 pt-8 border-t border-dark-700 text-center text-sm text-gray-500">
      <p>Data provided by NOAA Space Weather Prediction Center</p>
      <p class="mt-2">&copy; {currentYear} AuroraAlert. All rights reserved.</p>
    </div>
  </div>
</footer>