---
---
<header class="sticky top-0 z-50 backdrop-blur-lg bg-dark-900/70 border-b border-dark-700">
  <div class="container-custom py-4 flex justify-between items-center">
    <a href="/" class="flex items-center gap-2">
      <span class="w-8 h-8">
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="url(#paint0_linear)"/>
          <path d="M15 9C15 10.6569 13.6569 12 12 12C10.3431 12 9 10.6569 9 9C9 7.34315 10.3431 6 12 6C13.6569 6 15 7.34315 15 9Z" fill="url(#paint1_linear)"/>
          <path d="M8.5 15.5C8.5 16.3284 9.17157 17 10 17C10.8284 17 11.5 16.3284 11.5 15.5C11.5 14.6716 10.8284 14 10 14C9.17157 14 8.5 14.6716 8.5 15.5Z" fill="url(#paint1_linear)"/>
          <path d="M12.5 15.5C12.5 16.3284 13.1716 17 14 17C14.8284 17 15.5 16.3284 15.5 15.5C15.5 14.6716 14.8284 14 14 14C13.1716 14 12.5 14.6716 12.5 15.5Z" fill="url(#paint1_linear)"/>
          <defs>
            <linearGradient id="paint0_linear" x1="2" y1="12" x2="22" y2="12" gradientUnits="userSpaceOnUse">
              <stop stop-color="#4CAF50"/>
              <stop offset="0.5" stop-color="#9C27B0"/>
              <stop offset="1" stop-color="#2196F3"/>
            </linearGradient>
            <linearGradient id="paint1_linear" x1="9" y1="9" x2="15" y2="9" gradientUnits="userSpaceOnUse">
              <stop stop-color="#4CAF50"/>
              <stop offset="0.5" stop-color="#9C27B0"/>
              <stop offset="1" stop-color="#2196F3"/>
            </linearGradient>
          </defs>
        </svg>
      </span>
      <span class="text-xl font-display font-bold">AuroraAlert</span>
    </a>
    
    <nav class="hidden md:flex items-center space-x-6">
      <a href="#forecast" class="hover:text-primary-300 transition-colors">Forecast</a>
      <a href="#kp-index" class="hover:text-primary-300 transition-colors">KP Index</a>
      <a href="#aurora-power" class="hover:text-primary-300 transition-colors">Aurora Power</a>
      <a href="#magnetic-field" class="hover:text-primary-300 transition-colors">Magnetic Data</a>
      <a href="#aurora-map" class="hover:text-primary-300 transition-colors">Aurora Map</a>
    </nav>
    
    <div class="flex items-center gap-4">
      <!-- Timezone Selector -->
      <div class="relative">
        <button 
          id="timezone-button" 
          class="hidden md:flex items-center gap-2 px-3 py-2 rounded-md bg-dark-700 hover:bg-dark-600 transition-colors text-sm"
          aria-label="Select Timezone"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span id="current-timezone-text">UTC</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform" id="timezone-chevron" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        
        <!-- Timezone Dropdown -->
        <div id="timezone-dropdown" class="absolute right-0 top-full mt-2 w-64 bg-dark-800 rounded-lg shadow-xl border border-dark-600 hidden z-50">
          <div class="p-2">
            <div class="text-xs text-gray-400 mb-2 px-2">Select your timezone:</div>
            <div id="timezone-options" class="space-y-1">
              <!-- Options will be populated by JavaScript -->
            </div>
          </div>
        </div>
      </div>
      
      <button class="md:hidden" aria-label="Menu" id="mobile-menu-button">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
    </div>
  </div>
  
  <!-- Mobile menu -->
  <div class="md:hidden hidden" id="mobile-menu">
    <div class="container-custom py-4 flex flex-col space-y-4 border-t border-dark-700">
      <a href="#forecast" class="hover:text-primary-300 transition-colors">Forecast</a>
      <a href="#kp-index" class="hover:text-primary-300 transition-colors">KP Index</a>
      <a href="#aurora-power" class="hover:text-primary-300 transition-colors">Aurora Power</a>
      <a href="#magnetic-field" class="hover:text-primary-300 transition-colors">Magnetic Data</a>
      <a href="#aurora-map" class="hover:text-primary-300 transition-colors">Aurora Map</a>
      
      <!-- Mobile Timezone Selector -->
      <div class="border-t border-dark-600 pt-4">
        <div class="text-xs text-gray-400 mb-2">Timezone:</div>
        <select id="mobile-timezone-select" class="w-full bg-dark-700 text-white border border-dark-600 rounded-md px-3 py-2 text-sm">
          <!-- Options will be populated by JavaScript -->
        </select>
      </div>
    </div>
  </div>
</header>

<script>
  import { 
    initTimezone, 
    getTimezoneOptions, 
    setTimezone, 
    getCurrentTimezone, 
    getTimezoneDisplayText,
    addTimezoneChangeListener
  } from '../utils/timezone.js';

  // Mobile menu toggle
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', () => {
      mobileMenu.classList.toggle('hidden');
    });
  }

  // Timezone selector functionality
  function initTimezoneSelector() {
    // Initialize timezone system
    const currentTimezone = initTimezone();
    const timezoneOptions = getTimezoneOptions();

    // Update current timezone display
    function updateTimezoneDisplay() {
      const currentTimezoneText = document.getElementById('current-timezone-text');
      if (currentTimezoneText) {
        const timezoneInfo = getTimezoneDisplayText();
        currentTimezoneText.textContent = timezoneInfo.split(' (')[0]; // 只显示简短版本
      }
    }

    // Populate desktop dropdown options
    function populateDesktopOptions() {
      const optionsContainer = document.getElementById('timezone-options');
      if (!optionsContainer) return;

      optionsContainer.innerHTML = '';
      
      timezoneOptions.forEach(option => {
        const button = document.createElement('button');
        button.className = 'w-full text-left px-3 py-2 text-sm rounded hover:bg-dark-600 transition-colors';
        button.innerHTML = `
          <div class="font-medium">${option.label}</div>
          <div class="text-xs text-gray-400">${option.offset}</div>
        `;
        
        if (option.value === getCurrentTimezone()) {
          button.classList.add('bg-primary-600', 'text-white');
        }
        
        button.addEventListener('click', () => {
          setTimezone(option.value);
          toggleDropdown();
        });
        
        optionsContainer.appendChild(button);
      });
    }

    // Populate mobile select options
    function populateMobileOptions() {
      const mobileSelect = document.getElementById('mobile-timezone-select');
      if (!mobileSelect) return;

      mobileSelect.innerHTML = '';
      
      timezoneOptions.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option.value;
        optionElement.textContent = option.label;
        
        if (option.value === getCurrentTimezone()) {
          optionElement.selected = true;
        }
        
        mobileSelect.appendChild(optionElement);
      });

      // Add change listener
      mobileSelect.addEventListener('change', (e) => {
        const target = e.target as HTMLSelectElement;
        if (target) {
          setTimezone(target.value);
        }
      });
    }

    // Toggle dropdown visibility
    function toggleDropdown() {
      const dropdown = document.getElementById('timezone-dropdown');
      const chevron = document.getElementById('timezone-chevron');
      
      if (dropdown && chevron) {
        const isHidden = dropdown.classList.contains('hidden');
        
        if (isHidden) {
          dropdown.classList.remove('hidden');
          chevron.style.transform = 'rotate(180deg)';
          populateDesktopOptions(); // 每次打开时更新选项
        } else {
          dropdown.classList.add('hidden');
          chevron.style.transform = 'rotate(0deg)';
        }
      }
    }

    // Setup desktop timezone button
    const timezoneButton = document.getElementById('timezone-button');
    if (timezoneButton) {
      timezoneButton.addEventListener('click', toggleDropdown);
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
      const dropdown = document.getElementById('timezone-dropdown');
      const button = document.getElementById('timezone-button');
      const target = e.target as Node;
      
      if (dropdown && button && target && !dropdown.contains(target) && !button.contains(target)) {
        dropdown.classList.add('hidden');
        const chevron = document.getElementById('timezone-chevron');
        if (chevron) {
          chevron.style.transform = 'rotate(0deg)';
        }
      }
    });

    // Listen for timezone changes
    addTimezoneChangeListener(() => {
      updateTimezoneDisplay();
      populateDesktopOptions();
      populateMobileOptions();
    });

    // Initial setup
    updateTimezoneDisplay();
    populateDesktopOptions();
    populateMobileOptions();
  }

  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    initTimezoneSelector();
  });
</script>