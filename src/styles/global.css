@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans antialiased text-white;
    background-color: #09090f;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-display font-bold;
  }
}

@layer components {
  .container-custom {
    @apply container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }
  
  .card {
    @apply bg-dark-800/80 backdrop-blur-lg border border-dark-600 rounded-lg shadow-lg overflow-hidden;
  }
  
  .tab {
    @apply px-4 py-2 font-medium rounded-md transition duration-200;
  }
  
  .tab.active {
    @apply bg-primary-500/20 text-primary-300;
  }
  
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition duration-200;
  }
  
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white;
  }
  
  .btn-outline {
    @apply border border-primary-600 text-primary-400 hover:bg-primary-600/10;
  }
  
  .gradient-text {
    @apply font-bold bg-clip-text text-transparent bg-gradient-to-r from-aurora-green via-aurora-purple to-aurora-blue;
  }
}

/* Stars background */
.stars-bg {
  background-image: 
    radial-gradient(2px 2px at 20px 30px, #eaeaea, rgba(0,0,0,0)),
    radial-gradient(2px 2px at 40px 70px, #ffffff, rgba(0,0,0,0)),
    radial-gradient(1px 1px at 90px 40px, #f0f0f0, rgba(0,0,0,0)),
    radial-gradient(1px 1px at 130px 80px, #ffffff, rgba(0,0,0,0)),
    radial-gradient(2px 2px at 160px 120px, #dddddd, rgba(0,0,0,0));
  background-repeat: repeat;
  background-size: 200px 200px;
}

/* Loading animation for images */
.image-loader {
  background: linear-gradient(90deg, 
    rgba(49, 49, 77, 0) 0%, 
    rgba(49, 49, 77, 0.5) 50%, 
    rgba(49, 49, 77, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite linear;
}

/* KP Index colors */
.kp-0, .kp-1 { @apply bg-aurora-green/30 text-aurora-green; }
.kp-2, .kp-3 { @apply bg-aurora-green/50 text-aurora-green; }
.kp-4 { @apply bg-aurora-yellow/30 text-aurora-yellow; }
.kp-5 { @apply bg-aurora-yellow/50 text-aurora-yellow; }
.kp-6 { @apply bg-aurora-orange/30 text-aurora-orange; }
.kp-7 { @apply bg-aurora-orange/50 text-aurora-orange; }
.kp-8, .kp-9 { @apply bg-aurora-red/30 text-aurora-red; }