// 时区管理工具
const TIMEZONE_OPTIONS = [
  { 
    name: 'UTC', 
    value: 'UTC', 
    label: 'UTC (Universal Time)',
    offset: '+00:00'
  },
  { 
    name: 'Australia/Sydney', 
    value: 'Australia/Sydney', 
    label: 'AEDT (Sydney)',
    offset: '+11:00'
  },
  { 
    name: 'Australia/Melbourne', 
    value: 'Australia/Melbourne', 
    label: 'AEDT (Melbourne)',
    offset: '+11:00'
  },
  { 
    name: 'Australia/Perth', 
    value: 'Australia/Perth', 
    label: 'AWST (Perth)',
    offset: '+08:00'
  },
  { 
    name: 'Australia/Adelaide', 
    value: 'Australia/Adelaide', 
    label: 'ACDT (Adelaide)',
    offset: '+10:30'
  },
  { 
    name: 'Pacific/Auckland', 
    value: 'Pacific/Auckland', 
    label: 'NZDT (Auckland)',
    offset: '+13:00'
  },
  { 
    name: 'America/New_York', 
    value: 'America/New_York', 
    label: 'EST (New York)',
    offset: '-05:00'
  },
  { 
    name: 'Europe/London', 
    value: 'Europe/London', 
    label: 'GMT (London)',
    offset: '+00:00'
  }
];

const STORAGE_KEY = 'aurora-timezone';
let currentTimezone = 'UTC';
let changeListeners = [];

// 初始化时区设置
export function initTimezone() {
  // 从本地存储读取保存的时区
  const savedTimezone = localStorage.getItem(STORAGE_KEY);
  if (savedTimezone && TIMEZONE_OPTIONS.find(tz => tz.value === savedTimezone)) {
    currentTimezone = savedTimezone;
  } else {
    // 尝试自动检测用户时区
    const detectedTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const matchedTimezone = TIMEZONE_OPTIONS.find(tz => tz.value === detectedTimezone);
    currentTimezone = matchedTimezone ? detectedTimezone : 'UTC';
  }
  
  // 保存到本地存储
  localStorage.setItem(STORAGE_KEY, currentTimezone);
  
  return currentTimezone;
}

// 获取当前时区
export function getCurrentTimezone() {
  return currentTimezone;
}

// 设置时区
export function setTimezone(timezone) {
  if (!TIMEZONE_OPTIONS.find(tz => tz.value === timezone)) {
    console.warn(`不支持的时区: ${timezone}`);
    return;
  }
  
  currentTimezone = timezone;
  localStorage.setItem(STORAGE_KEY, timezone);
  
  // 通知所有监听器
  changeListeners.forEach(listener => {
    try {
      listener(timezone);
    } catch (error) {
      console.error('时区变化监听器错误:', error);
    }
  });
}

// 添加时区变化监听器
export function addTimezoneChangeListener(listener) {
  changeListeners.push(listener);
  
  // 返回移除监听器的函数
  return () => {
    const index = changeListeners.indexOf(listener);
    if (index > -1) {
      changeListeners.splice(index, 1);
    }
  };
}

// 获取时区选项列表
export function getTimezoneOptions() {
  return TIMEZONE_OPTIONS;
}

// 格式化时间到指定时区
export function formatTimeInTimezone(date, formatStr = 'MMM d, h:mm a', timezone = null) {
  const targetTimezone = timezone || currentTimezone;
  
  try {
    if (typeof date === 'string') {
      date = new Date(date);
    }
    
    // 使用Intl.DateTimeFormat进行时区转换
    const options = {
      timeZone: targetTimezone,
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    };
    
    if (formatStr.includes('HH:mm')) {
      options.hour12 = false;
      options.hour = '2-digit';
    }
    
    return new Intl.DateTimeFormat('en-US', options).format(date);
  } catch (error) {
    console.error('时间格式化错误:', error);
    return date.toLocaleString();
  }
}

// 获取时区信息
export function getTimezoneInfo(timezone = null) {
  const targetTimezone = timezone || currentTimezone;
  const option = TIMEZONE_OPTIONS.find(tz => tz.value === targetTimezone);
  return option || TIMEZONE_OPTIONS[0];
}

// 格式化时间用于Chart.js (需要特定格式)
export function formatChartTime(date, timezone = null) {
  const targetTimezone = timezone || currentTimezone;
  
  try {
    if (typeof date === 'string') {
      date = new Date(date);
    }
    
    // 对于图表，我们需要返回Date对象，但是要调整到目标时区
    const utcTime = date.getTime() + (date.getTimezoneOffset() * 60000);
    
    // 获取目标时区的偏移量
    const targetDate = new Date(utcTime);
    const targetTimeString = targetDate.toLocaleString('en-CA', {
      timeZone: targetTimezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
    
    return new Date(targetTimeString);
  } catch (error) {
    console.error('Chart时间格式化错误:', error);
    return date;
  }
}

// 简化的时间格式化函数
export function formatTime(date, timezone = null) {
  const targetTimezone = timezone || currentTimezone;
  
  try {
    if (typeof date === 'string') {
      date = new Date(date);
    }
    
    return new Intl.DateTimeFormat('en-US', {
      timeZone: targetTimezone,
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    }).format(date);
  } catch (error) {
    console.error('简化时间格式化错误:', error);
    return date.toLocaleString();
  }
}

// 获取相对于UTC的时区信息文本
export function getTimezoneDisplayText(timezone = null) {
  const targetTimezone = timezone || currentTimezone;
  const info = getTimezoneInfo(targetTimezone);
  return `${info.label} (${info.offset})`;
} 