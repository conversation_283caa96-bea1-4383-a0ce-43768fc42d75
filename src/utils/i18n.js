// 多语言支持系统 - 根据极光预报网站开发文档实现

const translations = {
  zh: {
    // 导航
    forecast: "极光预报",
    kpIndex: "KP指数",
    auroraPower: "极光强度",
    magneticData: "磁场数据",
    auroraMap: "极光地图",
    
    // 页面标题
    auroraForecast: "极光预报",
    currentKP: "当前KP指数",
    kpIndexForecast3Days: "KP指数3天预报",
    auroraIntensityIndex: "极光强度指数",
    magneticFieldBz: "磁场Bz分量",
    solarWind: "太阳风",
    interactiveAuroraMap: "交互式极光地图",
    
    // 状态文本
    quiet: "平静",
    active: "活跃",
    minorStorm: "轻微磁暴",
    majorStorm: "中等磁暴",
    severeStorm: "强磁暴",
    
    // 按钮文本
    viewForecast: "查看预报",
    checkKpIndex: "检查KP指数",
    hours24: "24小时",
    days3: "3天",
    days27: "27天",
    northernHemisphere: "北半球",
    southernHemisphere: "南半球",
    
    // 时间相关
    lastUpdated: "最后更新",
    now: "现在",
    tonight: "今晚",
    tomorrow: "明晚",
    
    // 描述文本
    trackAuroraRealTime: "实时追踪极光",
    auroraForecastDescription: "获取准确的极光预报，KP指数监测和北极光、南极光观测警报。",
    kpIndexDescription: "实时追踪地磁KP指数。数值越高表示地磁活动越强，看到极光的机会越大。",
    auroraPowerDescription: "通过我们的HPI（半球功率指数）图表实时监测极光活动的功率和强度。",
    magneticFieldDescription: "追踪直接影响极光形成的太阳风和磁场数据。",
    auroraMapDescription: "探索实时极光概率地图，查看北极光和南极光最可能出现的位置。",
    
    // 数据源
    dataSource: "数据来源",
    noaaSpaceWeather: "NOAA太空天气预报中心",
    
    // 错误信息
    loadingError: "加载失败，请稍后重试",
    dataNotAvailable: "数据暂不可用",
    
    // 图例和说明
    veryLow: "很低",
    low: "低",
    moderate: "中等", 
    high: "高",
    veryHigh: "很高",
    weak: "微弱",
    strong: "强",
    veryStrong: "很强",
    
    // 单位
    gigawatts: "吉瓦",
    nanotesla: "纳特斯拉",
    kmPerSecond: "公里/秒",
    probability: "概率"
  },
  
  en: {
    // Navigation
    forecast: "Forecast",
    kpIndex: "KP Index", 
    auroraPower: "Aurora Power",
    magneticData: "Magnetic Data",
    auroraMap: "Aurora Map",
    
    // Page titles
    auroraForecast: "Aurora Forecast",
    currentKP: "Current KP Index",
    kpIndexForecast3Days: "3-Day KP Index Forecast",
    auroraIntensityIndex: "Aurora Intensity Index",
    magneticFieldBz: "Magnetic Field Bz Component",
    solarWind: "Solar Wind",
    interactiveAuroraMap: "Interactive Aurora Map",
    
    // Status text
    quiet: "Quiet",
    active: "Active", 
    minorStorm: "Minor Storm",
    majorStorm: "Major Storm",
    severeStorm: "Severe Storm",
    
    // Button text
    viewForecast: "View Forecast",
    checkKpIndex: "Check KP Index",
    hours24: "24 Hours",
    days3: "3 Days",
    days27: "27 Days",
    northernHemisphere: "Northern Hemisphere",
    southernHemisphere: "Southern Hemisphere",
    
    // Time related
    lastUpdated: "Last updated",
    now: "Now",
    tonight: "Tonight",
    tomorrow: "Tomorrow",
    
    // Description text
    trackAuroraRealTime: "Track the Aurora in Real-Time",
    auroraForecastDescription: "Get accurate forecasts, KP index monitoring, and alerts for the Northern and Southern Lights.",
    kpIndexDescription: "Track the geomagnetic KP index in real-time. Higher values indicate stronger geomagnetic activity and better chances to see aurora.",
    auroraPowerDescription: "Monitor the power and intensity of aurora activity in real-time with our HPI (Hemispheric Power Index) chart.",
    magneticFieldDescription: "Track solar wind and magnetic field data that directly influence aurora formation.",
    auroraMapDescription: "Explore the real-time aurora probability map to see where the Northern and Southern Lights are most likely to appear.",
    
    // Data source
    dataSource: "Data Source",
    noaaSpaceWeather: "NOAA Space Weather Prediction Center",
    
    // Error messages
    loadingError: "Failed to load, please try again later",
    dataNotAvailable: "Data not available",
    
    // Legend and descriptions
    veryLow: "Very Low",
    low: "Low", 
    moderate: "Moderate",
    high: "High",
    veryHigh: "Very High",
    weak: "Weak",
    strong: "Strong", 
    veryStrong: "Very Strong",
    
    // Units
    gigawatts: "GW",
    nanotesla: "nT", 
    kmPerSecond: "km/s",
    probability: "Probability"
  }
};

// 当前语言
let currentLanguage = 'en';

// 获取浏览器语言偏好
function getBrowserLanguage() {
  const lang = navigator.language || navigator.languages[0];
  if (lang.startsWith('zh')) {
    return 'zh';
  }
  return 'en';
}

// 设置语言
export function setLanguage(lang) {
  if (translations[lang]) {
    currentLanguage = lang;
    localStorage.setItem('preferred-language', lang);
    
    // 触发语言变更事件
    window.dispatchEvent(new CustomEvent('languageChanged', { 
      detail: { language: lang } 
    }));
  }
}

// 获取当前语言
export function getCurrentLanguage() {
  return currentLanguage;
}

// 翻译函数
export function t(key, fallback = key) {
  const keys = key.split('.');
  let translation = translations[currentLanguage];
  
  for (const k of keys) {
    if (translation && translation[k]) {
      translation = translation[k];
    } else {
      return fallback;
    }
  }
  
  return translation || fallback;
}

// 初始化语言
export function initLanguage() {
  // 从localStorage获取保存的语言偏好
  const savedLang = localStorage.getItem('preferred-language');
  
  if (savedLang && translations[savedLang]) {
    currentLanguage = savedLang;
  } else {
    // 使用浏览器语言偏好
    currentLanguage = getBrowserLanguage();
  }
  
  return currentLanguage;
}

// 获取所有支持的语言
export function getSupportedLanguages() {
  return Object.keys(translations);
}

// 语言切换器组件数据
export function getLanguageSwitcherData() {
  return [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'zh', name: '中文', flag: '🇨🇳' }
  ];
} 