<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug NOAA Aurora API</title>
    <style>
        body { font-family: system-ui, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .loading { background: #333; color: #ccc; }
        .error { background: #d32f2f; color: white; }
        .success { background: #388e3c; color: white; }
        pre { background: #2d2d2d; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .stats { background: #1e3a8a; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .section { margin: 20px 0; border: 1px solid #444; padding: 15px; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>🌌 NOAA Aurora API Debug Tool</h1>
    <div id="status" class="status loading">正在获取NOAA极光数据...</div>
    
    <div class="section">
        <h2>API响应信息</h2>
        <div id="api-info"></div>
    </div>
    
    <div class="section">
        <h2>数据统计</h2>
        <div id="data-stats"></div>
    </div>
    
    <div class="section">
        <h2>坐标数据样本 (前10个点)</h2>
        <pre id="sample-data"></pre>
    </div>
    
    <div class="section">
        <h2>半球分离测试</h2>
        <div id="hemisphere-test"></div>
    </div>
    
    <script>
        function getCords180(long360, lat360) {
            const lon180 = long360 > 180 ? long360 - 360 : long360;
            const lat180 = lat360 > 180 ? lat360 - 360 : lat360;
            return [lon180, lat180];
        }
        
        function processAuroraData(data) {
            if (!data || !data.coordinates) return { northern: [], southern: [] };
            
            const northernData = [];
            const southernData = [];
            const coordinates = data.coordinates;
            
            for (const coord of coordinates) {
                const [longitude360, latitude360, intensity] = coord;
                const [longitude, latitude] = getCords180(longitude360, latitude360);
                
                if (intensity > 0) {
                    const point = { longitude, latitude, intensity };
                    
                    if (latitude >= 0) {
                        northernData.push(point);
                    } else {
                        southernData.push(point);
                    }
                }
            }
            
            return { northern: northernData, southern: southernData };
        }
        
        async function debugNoaaApi() {
            const statusEl = document.getElementById('status');
            const apiInfoEl = document.getElementById('api-info');
            const dataStatsEl = document.getElementById('data-stats');
            const sampleDataEl = document.getElementById('sample-data');
            const hemisphereTestEl = document.getElementById('hemisphere-test');
            
            try {
                statusEl.textContent = '正在连接NOAA API...';
                
                const response = await fetch('https://services.swpc.noaa.gov/json/ovation_aurora_latest.json');
                
                if (!response.ok) {
                    throw new Error(`HTTP错误! 状态: ${response.status}`);
                }
                
                statusEl.className = 'status success';
                statusEl.textContent = '✅ 成功获取NOAA极光数据!';
                
                const data = await response.json();
                console.log('完整API响应:', data);
                
                // API信息
                apiInfoEl.innerHTML = `
                    <div class="stats">
                        <p><strong>观测时间:</strong> ${data['Observation Time'] || data['Forecast Time'] || '未知'}</p>
                        <p><strong>响应状态:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>数据类型:</strong> ${typeof data}</p>
                        <p><strong>主要字段:</strong> ${Object.keys(data).join(', ')}</p>
                    </div>
                `;
                
                // 检查coordinates字段
                if (!data.coordinates) {
                    throw new Error('API响应中缺少coordinates字段!');
                }
                
                if (!Array.isArray(data.coordinates)) {
                    throw new Error('coordinates字段不是数组!');
                }
                
                // 数据统计
                const totalPoints = data.coordinates.length;
                const processed = processAuroraData(data);
                const activePoints = processed.northern.length + processed.southern.length;
                
                // 强度统计
                const allIntensities = data.coordinates.map(coord => coord[2]).filter(i => i > 0);
                const maxIntensity = allIntensities.length > 0 ? Math.max(...allIntensities) : 0;
                const avgIntensity = allIntensities.length > 0 ? (allIntensities.reduce((a, b) => a + b, 0) / allIntensities.length) : 0;
                
                dataStatsEl.innerHTML = `
                    <div class="stats">
                        <p><strong>总数据点:</strong> ${totalPoints}</p>
                        <p><strong>活跃极光点:</strong> ${activePoints} (${((activePoints/totalPoints)*100).toFixed(1)}%)</p>
                        <p><strong>北半球点数:</strong> ${processed.northern.length}</p>
                        <p><strong>南半球点数:</strong> ${processed.southern.length}</p>
                        <p><strong>最大强度:</strong> ${maxIntensity.toFixed(2)}</p>
                        <p><strong>平均强度:</strong> ${avgIntensity.toFixed(2)}</p>
                    </div>
                `;
                
                // 样本数据
                const sampleCoords = data.coordinates.slice(0, 10);
                sampleDataEl.textContent = JSON.stringify(sampleCoords, null, 2);
                
                // 半球测试
                const northernSample = processed.northern.slice(0, 3);
                const southernSample = processed.southern.slice(0, 3);
                
                hemisphereTestEl.innerHTML = `
                    <div class="stats">
                        <h3>北半球样本 (前3个点):</h3>
                        <pre>${JSON.stringify(northernSample, null, 2)}</pre>
                        
                        <h3>南半球样本 (前3个点):</h3>
                        <pre>${JSON.stringify(southernSample, null, 2)}</pre>
                    </div>
                `;
                
                // 检查是否有足够的数据进行渲染
                if (activePoints === 0) {
                    statusEl.className = 'status error';
                    statusEl.textContent = '⚠️ 警告: 当前没有活跃的极光数据点!';
                }
                
            } catch (error) {
                console.error('NOAA API调试失败:', error);
                statusEl.className = 'status error';
                statusEl.textContent = `❌ 错误: ${error.message}`;
                
                apiInfoEl.innerHTML = `<div class="stats error">API调用失败: ${error.message}</div>`;
            }
        }
        
        // 运行调试
        debugNoaaApi();
    </script>
</body>
</html>
