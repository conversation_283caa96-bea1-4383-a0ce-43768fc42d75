# 极光预报网站开发技术文档

## 项目概述

基于现有Flutter应用的极光预报功能，开发一个Web版本的极光预报网站。该网站提供实时极光监测、预报数据展示、交互式地图等功能。

## 核心功能模块

### 1. 极光预报图像展示

#### 功能描述
展示NOAA提供的极光预报图像，包括4个主要视图：
- 北极30分钟预报
- 南极30分钟预报  
- 今晚北美地区预报
- 明晚北美地区预报

#### 技术实现
```javascript
// 图像数据源
const auroraImageSources = {
  north30min: "https://services.swpc.noaa.gov/images/animations/ovation/north/latest.jpg",
  south30min: "https://services.swpc.noaa.gov/images/animations/ovation/south/latest.jpg", 
  tonightNA: "https://services.swpc.noaa.gov/experimental/images/aurora_dashboard/tonights_static_viewline_forecast.png",
  tomorrowNA: "https://services.swpc.noaa.gov/experimental/images/aurora_dashboard/tomorrow_nights_static_viewline_forecast.png"
};

// 添加时间戳防止缓存
function getImageUrlWithTimestamp(baseUrl) {
  return `${baseUrl}?${Date.now()}`;
}
```

#### UI组件
- 标签页切换器（可横向滚动）
- 图像容器（支持点击全屏查看）
- 加载状态指示器

### 2. KP指数监测系统

#### 功能描述
实时监测和展示地磁KP指数，包括：
- 当前KP值高亮显示
- 过去6小时到未来3天的KP值柱状图
- 每日KP值范围统计
- 27天长期预报

#### 数据结构
```javascript
// KP数据结构
interface KPData {
  time: string;           // ISO 8601 时间格式
  kp: number;            // KP值 (0-9)
  value: string;         // KP值字符串表示
}

// 预报数据结构  
interface ForecastData {
  date: string;          // 日期
  kpIndex: number;       // 预报KP值
}
```

#### API接口
```javascript
// 获取KP指数数据
GET https://nodeapi.knockdream.com/api/kp-index
Response: Array<{
  time_tag: string,
  kp: number
}>

// 获取27天预报数据
GET https://nodeapi.knockdream.com/api/aurora-forecast  
Response: Array<{
  date: string,
  kpIndex: number
}>
```

#### 可视化实现
- 使用Chart.js或D3.js绘制柱状图
- KP值颜色映射：
  ```javascript
  function getKPColor(kp) {
    if (kp <= 1) return '#C8E6C9';      // 浅绿
    if (kp <= 2) return '#81C784';      // 绿色
    if (kp <= 3) return '#4CAF50';      // 深绿
    if (kp <= 4) return '#FFF176';      // 浅黄
    if (kp <= 5) return '#FFEB3B';      // 黄色
    if (kp <= 6) return '#FFB74D';      // 浅橙
    if (kp <= 7) return '#FF9800';      // 橙色
    if (kp <= 8) return '#EF5350';      // 浅红
    return '#F44336';                   // 红色
  }
  ```

### 3. 极光强度指数(HPI)图表

#### 功能描述
显示极光强度的时间序列图表，帮助用户了解极光活动强度变化趋势。

#### 数据接口
```javascript
GET https://nodeapi.knockdream.com/api/aurora-power
Response: Array<{
  forecast: string,      // 时间
  northPower: number     // 北半球强度值
}>
```

#### 图表特性
- 平滑曲线图
- 当前时间垂直指示线
- 交互式工具提示
- 渐变填充区域

### 4. 磁场监测系统

#### 功能描述
监测太阳风磁场Bz分量，这是极光活动的重要指标。

#### 数据结构
```javascript
interface MagneticFieldData {
  time_tag: string;      // 测量时间
  bz_gsm: number;        // Bz分量值(nT)
}

interface SolarWindData {
  time_tag: string;      // 测量时间  
  speed: number;         // 太阳风速度(km/s)
  density: number;       // 密度(protons/cm³)
}
```

#### API接口
```javascript
GET https://nodeapi.knockdream.com/api/magnetic-field
GET https://nodeapi.knockdream.com/api/solar-wind
```

#### 时间校正算法
```javascript
// 计算太阳风从L1点到地球的传播时间
function calculateEarthTime(solarWindData) {
  const L1_DISTANCE = 1500000; // km
  const lastData = solarWindData[solarWindData.length - 1];
  const speed = lastData.speed;
  const timeAtL1 = new Date(lastData.time_tag);
  
  const travelTimeMinutes = (L1_DISTANCE / speed) / 60;
  const adjustedTime = new Date(Date.now() - travelTimeMinutes * 60000);
  
  return adjustedTime;
}
```

### 5. 交互式极光地图

#### 功能描述
在地图上显示极光出现概率的热力图层。

#### 实现架构
```javascript
// 地图瓦片生成器
class AuroraTileProvider {
  constructor(auroraData) {
    this.auroraData = auroraData;
    this.cache = new Map();
    this.preprocessData();
  }
  
  // 数据预处理：转换为网格数据
  preprocessData() {
    const gridSize = 360;
    this.gridData = Array(gridSize).fill().map(() => Array(gridSize).fill(0));
    
    for (const point of this.auroraData) {
      const lat = Math.floor(point[1] + 90);
      let lng = Math.floor(point[0] + 180);
      if (lng >= gridSize) lng -= gridSize;
      
      // 只处理高纬度地区 (>25° 或 <-25°)
      if ((point[1] > 25 || point[1] < -25) && 
          lat >= 0 && lat < gridSize && 
          lng >= 0 && lng < gridSize) {
        this.gridData[lat][lng] = Math.max(this.gridData[lat][lng], point[2]);
      }
    }
  }
  
  // 生成地图瓦片
  generateTile(x, y, z) {
    const resolution = this.getResolutionForZoom(z);
    const bounds = this.getTileBounds(x, y, z);
    const probabilities = this.calculateProbabilities(bounds, resolution);
    
    return this.renderTile(probabilities, resolution);
  }
}
```

#### 概率颜色映射
```javascript
function getProbabilityColor(probability) {
  if (probability > 75) return 'rgba(255, 0, 0, 0.7)';      // 红色
  if (probability > 50) return 'rgba(255, 128, 0, 0.7)';    // 橙色  
  if (probability > 25) return 'rgba(255, 255, 0, 0.7)';    // 黄色
  if (probability > 10) return 'rgba(128, 255, 0, 0.7)';    // 黄绿
  if (probability > 5)  return 'rgba(0, 255, 0, 0.7)';      // 绿色
  if (probability > 2)  return 'rgba(0, 128, 0, 0.7)';      // 深绿
  return probability > 0 
    ? 'rgba(0, 64, 0, 0.7)'     // 暗绿
    : 'rgba(0, 0, 0, 0)';       // 透明
}
```

### 6. 数据缓存系统

#### 缓存策略
```javascript
const CACHE_CONFIG = {
  KP_DATA: { duration: 10 * 60 * 1000 },           // 10分钟
  FORECAST_DATA: { duration: 60 * 60 * 1000 },     // 1小时  
  AURORA_POWER: { duration: 3 * 60 * 1000 },       // 3分钟
  MAGNETIC_FIELD: { duration: 5 * 60 * 1000 },     // 5分钟
  SOLAR_WIND: { duration: 5 * 60 * 1000 }          // 5分钟
};

class DataCache {
  constructor() {
    this.cache = new Map();
  }
  
  async get(key, fetcher, duration) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < duration) {
      return cached.data;
    }
    
    const data = await fetcher();
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
    
    return data;
  }
}
```

### 7. 多语言支持

#### 支持语言
- 中文 (zh)
- 英文 (en)

#### 关键术语对照
```javascript
const translations = {
  zh: {
    auroraForecast: "极光预报",
    currentKP: "当前KP指数",
    kpIndexForecast3Days: "KP指数3天预报", 
    auroraIntensityIndex: "极光强度指数",
    magneticFieldBz: "磁场Bz分量",
    // ... 更多翻译
  },
  en: {
    auroraForecast: "Aurora Forecast",
    currentKP: "Current KP Index",
    kpIndexForecast3Days: "3-Day KP Index Forecast",
    auroraIntensityIndex: "Aurora Intensity Index", 
    magneticFieldBz: "Magnetic Field Bz Component",
    // ... more translations
  }
};
```

## 技术栈建议

### 前端技术
- **框架**: React.js 或 Vue.js
- **图表库**: Chart.js 或 D3.js
- **地图库**: Leaflet.js 或 MapBox GL JS
- **UI框架**: Material-UI 或 Ant Design
- **状态管理**: Redux 或 Vuex
- **HTTP客户端**: Axios

### 后端技术
- **服务器**: Node.js + Express 或 Python + FastAPI
- **数据库**: PostgreSQL (时序数据) + Redis (缓存)
- **定时任务**: Cron jobs 或 Celery
- **监控**: PM2 或 Supervisor

### 部署架构
- **容器化**: Docker
- **反向代理**: Nginx
- **CDN**: CloudFlare
- **监控**: Prometheus + Grafana

## 性能优化策略

### 1. 数据优化
- 实施分层缓存策略
- 数据压缩传输
- 增量数据更新
- 懒加载非关键数据

### 2. 渲染优化
- Canvas/WebGL 渲染地图瓦片
- 虚拟滚动长列表
- 图表数据抽样显示
- 图像预加载

### 3. 网络优化
- HTTP/2 服务器推送
- 资源压缩 (Gzip/Brotli)
- CDN 加速静态资源
- 服务工作者缓存

## 开发流程建议

### 阶段1: 基础框架搭建
1. 项目初始化和依赖配置
2. 路由和页面结构
3. 基础UI组件开发
4. 多语言系统集成

### 阶段2: 数据展示功能
1. KP指数图表实现
2. 极光图像展示器
3. 磁场数据可视化
4. 数据缓存系统

### 阶段3: 交互功能
1. 极光地图集成
2. 实时数据更新
3. 响应式设计适配
4. 用户交互优化

### 阶段4: 优化和部署
1. 性能调优
2. 错误处理完善
3. 监控系统部署
4. 生产环境发布

## 测试策略

### 单元测试
- 数据处理函数测试
- 图表组件测试
- 缓存系统测试

### 集成测试  
- API接口测试
- 地图瓦片生成测试
- 实时数据流测试

### 端到端测试
- 用户操作流程测试
- 跨浏览器兼容性测试
- 移动设备适配测试

## 维护和监控

### 数据质量监控
- API响应时间监控
- 数据完整性检查
- 异常数据告警

### 系统性能监控
- 页面加载速度
- 内存使用情况
- 缓存命中率

### 用户体验监控
- 页面访问统计
- 用户行为分析
- 错误率监控

---

此文档提供了完整的技术实现指南，开发团队可以基于此文档进行详细的技术方案设计和开发实施。建议在开发过程中根据实际需求调整和完善相关技术细节。 