<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aurora Map Test</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body { 
            font-family: system-ui, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #1a1a1a; 
            color: #fff; 
        }
        #map { 
            height: 500px; 
            width: 100%; 
            border-radius: 8px; 
            margin: 20px 0; 
        }
        .status { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px; 
            background: #333; 
        }
        .success { background: #388e3c; }
        .error { background: #d32f2f; }
        .info { background: #1976d2; }
    </style>
</head>
<body>
    <h1>🌌 Aurora Map Test</h1>
    <div id="status" class="status">初始化中...</div>
    <div id="map"></div>
    <div id="debug-info" class="status"></div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        let map;
        let northernLayer;
        let southernLayer;
        let layerControl;

        // Convert from NOAA's 0-360 system to standard -180/180 for mapping
        function getCords180(long360, lat360) {
            const lon180 = long360 > 180 ? long360 - 360 : long360;
            const lat180 = lat360 > 180 ? lat360 - 360 : lat360;
            return [lon180, lat180];
        }

        // Northern Aurora color mapping
        function getNorthernAuroraColor(intensity) {
            if (intensity >= 15) return 'rgba(0, 255, 255, 0.9)';
            if (intensity >= 10) return 'rgba(0, 200, 255, 0.8)';
            if (intensity >= 7) return 'rgba(0, 255, 150, 0.8)';
            if (intensity >= 4) return 'rgba(0, 255, 100, 0.7)';
            if (intensity >= 1) return 'rgba(0, 180, 80, 0.6)';
            return 'rgba(0, 0, 0, 0)';
        }

        // Southern Aurora color mapping
        function getSouthernAuroraColor(intensity) {
            if (intensity >= 15) return 'rgba(255, 0, 255, 0.9)';
            if (intensity >= 10) return 'rgba(200, 0, 255, 0.8)';
            if (intensity >= 7) return 'rgba(255, 100, 200, 0.8)';
            if (intensity >= 4) return 'rgba(255, 150, 150, 0.7)';
            if (intensity >= 1) return 'rgba(180, 100, 150, 0.6)';
            return 'rgba(0, 0, 0, 0)';
        }

        function getAuroraColor(intensity, hemisphere) {
            return hemisphere === 'northern' 
                ? getNorthernAuroraColor(intensity)
                : getSouthernAuroraColor(intensity);
        }

        function getRadiusForIntensity(intensity, hemisphere) {
            let baseRadius = 0;
            if (intensity >= 15) baseRadius = 150000;
            else if (intensity >= 10) baseRadius = 110000;
            else if (intensity >= 7) baseRadius = 80000;
            else if (intensity >= 4) baseRadius = 60000;
            else if (intensity >= 1) baseRadius = 40000;
            
            const hemisphereMultiplier = hemisphere === 'southern' ? 1.2 : 1.0;
            return Math.round(baseRadius * hemisphereMultiplier);
        }

        function processAuroraData(data) {
            if (!data || !data.coordinates) return { northern: [], southern: [] };
            
            const northernData = [];
            const southernData = [];
            
            for (const coord of data.coordinates) {
                const [longitude360, latitude360, intensity] = coord;
                const [longitude, latitude] = getCords180(longitude360, latitude360);
                
                if (intensity > 0) {
                    const point = { longitude, latitude, intensity };
                    
                    if (latitude >= 0) {
                        northernData.push(point);
                    } else {
                        southernData.push(point);
                    }
                }
            }
            
            return { northern: northernData, southern: southernData };
        }

        function createAuroraLayer(hemisphereData, hemisphere) {
            if (!hemisphereData || hemisphereData.length === 0) return null;
            
            const circles = [];
            
            for (const point of hemisphereData) {
                const { longitude, latitude, intensity } = point;
                const radius = getRadiusForIntensity(intensity, hemisphere);
                const color = getAuroraColor(intensity, hemisphere);
                
                if (radius > 0) {
                    const circle = L.circle([latitude, longitude], {
                        color: color,
                        fillColor: color,
                        fillOpacity: 0.6,
                        radius: radius,
                        weight: 1,
                        opacity: 0.8
                    }).bindTooltip(
                        `<div>
                            <strong>${hemisphere === 'northern' ? '🌌 Aurora Borealis' : '🌠 Aurora Australis'}</strong><br>
                            <strong>强度:</strong> ${intensity.toFixed(1)}<br>
                            <strong>位置:</strong> ${latitude.toFixed(2)}°, ${longitude.toFixed(2)}°
                        </div>`
                    );
                    
                    circles.push(circle);
                }
            }
            
            return circles.length > 0 ? L.layerGroup(circles) : null;
        }

        async function fetchAuroraData() {
            try {
                const response = await fetch('https://services.swpc.noaa.gov/json/ovation_aurora_latest.json');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error('Error fetching aurora data:', error);
                return null;
            }
        }

        async function initMap() {
            const statusEl = document.getElementById('status');
            const debugEl = document.getElementById('debug-info');
            
            try {
                statusEl.textContent = '初始化地图...';
                
                // Initialize map
                map = L.map('map').setView([65, -100], 3);
                
                // Add tile layer
                L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
                    attribution: '&copy; OpenStreetMap contributors &copy; CARTO',
                    subdomains: 'abcd',
                    maxZoom: 19
                }).addTo(map);
                
                statusEl.textContent = '获取极光数据...';
                
                // Fetch and process data
                const data = await fetchAuroraData();
                if (!data) {
                    throw new Error('无法获取极光数据');
                }
                
                const hemisphereData = processAuroraData(data);
                
                debugEl.innerHTML = `
                    <div class="info">
                        <strong>数据统计:</strong><br>
                        总数据点: ${data.coordinates.length}<br>
                        北半球: ${hemisphereData.northern.length} 点<br>
                        南半球: ${hemisphereData.southern.length} 点<br>
                        观测时间: ${data['Observation Time'] || data['Forecast Time'] || '未知'}
                    </div>
                `;
                
                // Create layers
                northernLayer = createAuroraLayer(hemisphereData.northern, 'northern');
                southernLayer = createAuroraLayer(hemisphereData.southern, 'southern');
                
                // Setup layer control
                layerControl = L.control.layers({}, {}, {
                    position: 'topright',
                    collapsed: false
                }).addTo(map);
                
                // Add layers
                if (northernLayer) {
                    layerControl.addOverlay(northernLayer, '🌌 Aurora Borealis (Northern)');
                    northernLayer.addTo(map);
                }
                
                if (southernLayer) {
                    layerControl.addOverlay(southernLayer, '🌠 Aurora Australis (Southern)');
                    southernLayer.addTo(map);
                }
                
                // Fit bounds
                const allLayers = [];
                if (northernLayer) allLayers.push(...northernLayer.getLayers());
                if (southernLayer) allLayers.push(...southernLayer.getLayers());
                
                if (allLayers.length > 0) {
                    const group = new L.featureGroup(allLayers);
                    map.fitBounds(group.getBounds(), { padding: [20, 20] });
                }
                
                statusEl.className = 'status success';
                statusEl.textContent = '✅ 极光地图加载成功！';
                
            } catch (error) {
                console.error('Map initialization failed:', error);
                statusEl.className = 'status error';
                statusEl.textContent = `❌ 错误: ${error.message}`;
            }
        }

        // Initialize when page loads
        initMap();
    </script>
</body>
</html>
