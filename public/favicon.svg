<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="64" cy="64" r="60" stroke="url(#paint0_linear)" stroke-width="8"/>
  <circle cx="64" cy="40" r="16" fill="url(#paint1_linear)"/>
  <circle cx="40" cy="88" r="8" fill="url(#paint1_linear)"/>
  <circle cx="88" cy="88" r="8" fill="url(#paint1_linear)"/>
  <defs>
    <linearGradient id="paint0_linear" x1="4" y1="64" x2="124" y2="64" gradientUnits="userSpaceOnUse">
      <stop stop-color="#4CAF50"/>
      <stop offset="0.5" stop-color="#9C27B0"/>
      <stop offset="1" stop-color="#2196F3"/>
    </linearGradient>
    <linearGradient id="paint1_linear" x1="48" y1="40" x2="80" y2="40" gradientUnits="userSpaceOnUse">
      <stop stop-color="#4CAF50"/>
      <stop offset="0.5" stop-color="#9C27B0"/>
      <stop offset="1" stop-color="#2196F3"/>
    </linearGradient>
  </defs>
</svg>