/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f4ff',
          100: '#dbe4ff',
          200: '#bccaff',
          300: '#8ea6ff',
          400: '#5b78ff',
          500: '#3b4eff',
          600: '#2b2ef7',
          700: '#2421dd',
          800: '#231eb2',
          900: '#231e8b',
          950: '#171252',
        },
        aurora: {
          green: '#4CAF50',
          yellow: '#FFEB3B',
          orange: '#FF9800',
          red: '#F44336',
          purple: '#9C27B0',
          blue: '#2196F3',
        },
        dark: {
          100: '#d5d6d7',
          200: '#abadb0',
          300: '#818388',
          400: '#575a61',
          500: '#2d3139',
          600: '#24272e',
          700: '#1b1d22',
          800: '#121417',
          900: '#090a0b',
        },
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        display: ['Space Grotesk', 'Inter', 'system-ui', 'sans-serif'],
      },
      backdropFilter: {
        'none': 'none',
        'blur': 'blur(20px)',
      },
      animation: {
        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'shimmer': 'shimmer 2s infinite linear',
      },
      keyframes: {
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
      },
    },
  },
  plugins: [],
};