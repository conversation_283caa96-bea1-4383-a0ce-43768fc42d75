<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NOAA Aurora API Test</title>
    <style>
        body { font-family: system-ui, sans-serif; margin: 20px; }
        .loading { color: #666; }
        .error { color: red; }
        .success { color: green; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .stats { background: #e3f2fd; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>NOAA Aurora API Test</h1>
    <div id="status" class="loading">Loading aurora data from NOAA...</div>
    <div id="results"></div>
    
    <script>
        function getCords180(long360, lat360) {
            const lon180 = long360 > 180 ? long360 - 360 : long360;
            const lat180 = lat360 > 180 ? lat360 - 360 : lat360;
            return [lon180, lat180];
        }
        
        function processAuroraData(data) {
            if (!data || !data.coordinates) return { northern: [], southern: [] };
            
            const northernData = [];
            const southernData = [];
            const coordinates = data.coordinates;
            
            for (const coord of coordinates) {
                const [longitude360, latitude360, intensity] = coord;
                const [longitude, latitude] = getCords180(longitude360, latitude360);
                
                if (intensity > 0) {
                    const point = { longitude, latitude, intensity };
                    
                    if (latitude >= 0) {
                        northernData.push(point);
                    } else {
                        southernData.push(point);
                    }
                }
            }
            
            return { northern: northernData, southern: southernData };
        }
        
        async function testNoaaApi() {
            try {
                const response = await fetch('https://services.swpc.noaa.gov/json/ovation_aurora_latest.json');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                const processed = processAuroraData(data);
                
                // Calculate statistics
                const totalPoints = data.coordinates ? data.coordinates.length : 0;
                const activePoints = processed.northern.length + processed.southern.length;
                const northernIntensities = processed.northern.map(p => p.intensity);
                const southernIntensities = processed.southern.map(p => p.intensity);
                
                const maxNorthern = northernIntensities.length > 0 ? Math.max(...northernIntensities) : 0;
                const maxSouthern = southernIntensities.length > 0 ? Math.max(...southernIntensities) : 0;
                
                document.getElementById('status').innerHTML = '<span class="success">✓ Successfully loaded and processed NOAA aurora data!</span>';
                
                document.getElementById('results').innerHTML = `
                    <div class="stats">
                        <h2>Data Statistics:</h2>
                        <p><strong>Observation Time:</strong> ${data['Observation Time'] || data['Forecast Time'] || 'Unknown'}</p>
                        <p><strong>Total Data Points:</strong> ${totalPoints}</p>
                        <p><strong>Active Aurora Points:</strong> ${activePoints}</p>
                        <p><strong>Northern Hemisphere:</strong> ${processed.northern.length} points (max intensity: ${maxNorthern.toFixed(1)})</p>
                        <p><strong>Southern Hemisphere:</strong> ${processed.southern.length} points (max intensity: ${maxSouthern.toFixed(1)})</p>
                    </div>
                    
                    <h3>Sample Northern Hemisphere Data (first 5 points):</h3>
                    <pre>${JSON.stringify(processed.northern.slice(0, 5), null, 2)}</pre>
                    
                    <h3>Sample Southern Hemisphere Data (first 5 points):</h3>
                    <pre>${JSON.stringify(processed.southern.slice(0, 5), null, 2)}</pre>
                `;
                
                console.log('NOAA API test completed successfully:', {
                    totalPoints,
                    activePoints,
                    northern: processed.northern.length,
                    southern: processed.southern.length,
                    maxNorthern,
                    maxSouthern
                });
                
            } catch (error) {
                console.error('NOAA API test failed:', error);
                document.getElementById('status').innerHTML = `<span class="error">✗ Error loading NOAA data: ${error.message}</span>`;
            }
        }
        
        // Run the test
        testNoaaApi();
    </script>
</body>
</html>
